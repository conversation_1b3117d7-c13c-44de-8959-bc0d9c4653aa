<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

$pageTitle = 'Privacy Policy';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/modern-ui.css" rel="stylesheet">
    
    <style>
        .policy-hero {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 120px 0 80px;
        }
        
        .policy-content {
            background: white;
            border-radius: 20px;
            padding: 50px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-top: -50px;
            position: relative;
            z-index: 2;
        }
        
        .policy-section {
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .policy-section:last-child {
            border-bottom: none;
        }
        
        .policy-toc {
            background: var(--light-bg);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
        }
        
        .policy-toc a {
            color: var(--primary-color);
            text-decoration: none;
            display: block;
            padding: 8px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        .policy-toc a:hover {
            color: var(--secondary-color);
            padding-left: 10px;
            transition: all 0.3s ease;
        }
        
        .policy-toc a:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events/">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events/search.php">Search</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">Contact</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="booking/cart.php">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle" style="display: none;">0</span>
                        </a>
                    </li>
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'User'); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="user/dashboard.php">Dashboard</a></li>
                                <li><a class="dropdown-item" href="user/profile.php">Profile</a></li>
                                <?php if (isAdmin()): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="admin/">Admin Panel</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php">Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary-modern ms-2" href="auth/register.php">Sign Up</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Privacy Policy Hero Section -->
    <section class="policy-hero">
        <div class="container text-center">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">Privacy Policy</h1>
                    <p class="lead">Your privacy is important to us. Learn how we collect, use, and protect your information.</p>
                    <p class="text-white-50">Last updated: <?php echo date('F j, Y'); ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Privacy Policy Content -->
    <section class="py-5">
        <div class="container">
            <div class="policy-content">
                <!-- Table of Contents -->
                <div class="policy-toc">
                    <h4 class="fw-bold mb-3">
                        <i class="fas fa-list me-2"></i>
                        Table of Contents
                    </h4>
                    <a href="#information-collection">1. Information We Collect</a>
                    <a href="#information-use">2. How We Use Your Information</a>
                    <a href="#information-sharing">3. Information Sharing</a>
                    <a href="#data-security">4. Data Security</a>
                    <a href="#cookies">5. Cookies and Tracking</a>
                    <a href="#user-rights">6. Your Rights</a>
                    <a href="#data-retention">7. Data Retention</a>
                    <a href="#children-privacy">8. Children's Privacy</a>
                    <a href="#policy-changes">9. Policy Changes</a>
                    <a href="#contact-info">10. Contact Information</a>
                </div>

                <!-- Introduction -->
                <div class="policy-section">
                    <h3 class="fw-bold mb-3">Introduction</h3>
                    <p>
                        Welcome to ZARA-Events. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website and use our event booking services. Please read this privacy policy carefully. If you do not agree with the terms of this privacy policy, please do not access the site.
                    </p>
                    <p>
                        We reserve the right to make changes to this Privacy Policy at any time and for any reason. We will alert you about any changes by updating the "Last updated" date of this Privacy Policy.
                    </p>
                </div>

                <!-- Information Collection -->
                <div id="information-collection" class="policy-section">
                    <h3 class="fw-bold mb-3">
                        <i class="fas fa-database me-2 text-primary"></i>
                        1. Information We Collect
                    </h3>
                    
                    <h5 class="fw-bold mt-4">Personal Information</h5>
                    <p>We may collect personal information that you provide to us such as:</p>
                    <ul>
                        <li>Name and contact information (email address, phone number)</li>
                        <li>Billing and payment information</li>
                        <li>Event preferences and booking history</li>
                        <li>Profile information and preferences</li>
                        <li>Communication preferences</li>
                    </ul>
                    
                    <h5 class="fw-bold mt-4">Automatically Collected Information</h5>
                    <p>We may automatically collect certain information when you visit our website:</p>
                    <ul>
                        <li>IP address and browser information</li>
                        <li>Device information and operating system</li>
                        <li>Usage data and website interactions</li>
                        <li>Location information (with your consent)</li>
                        <li>Cookies and similar tracking technologies</li>
                    </ul>
                </div>

                <!-- Information Use -->
                <div id="information-use" class="policy-section">
                    <h3 class="fw-bold mb-3">
                        <i class="fas fa-cogs me-2 text-success"></i>
                        2. How We Use Your Information
                    </h3>
                    <p>We use the information we collect to:</p>
                    <ul>
                        <li>Process your event bookings and payments</li>
                        <li>Send booking confirmations and event updates</li>
                        <li>Provide customer support and respond to inquiries</li>
                        <li>Improve our website and services</li>
                        <li>Send promotional emails and newsletters (with your consent)</li>
                        <li>Prevent fraud and ensure security</li>
                        <li>Comply with legal obligations</li>
                        <li>Analyze usage patterns and preferences</li>
                    </ul>
                </div>

                <!-- Information Sharing -->
                <div id="information-sharing" class="policy-section">
                    <h3 class="fw-bold mb-3">
                        <i class="fas fa-share-alt me-2 text-warning"></i>
                        3. Information Sharing
                    </h3>
                    <p>We may share your information in the following situations:</p>
                    <ul>
                        <li><strong>Event Organizers:</strong> We share necessary information with event organizers for event management</li>
                        <li><strong>Payment Processors:</strong> We share payment information with secure payment service providers</li>
                        <li><strong>Service Providers:</strong> We may share information with trusted third-party service providers</li>
                        <li><strong>Legal Requirements:</strong> We may disclose information when required by law</li>
                        <li><strong>Business Transfers:</strong> Information may be transferred in case of merger or acquisition</li>
                        <li><strong>Consent:</strong> We may share information with your explicit consent</li>
                    </ul>
                    <p><strong>We do not sell, trade, or rent your personal information to third parties for marketing purposes.</strong></p>
                </div>

                <!-- Data Security -->
                <div id="data-security" class="policy-section">
                    <h3 class="fw-bold mb-3">
                        <i class="fas fa-shield-alt me-2 text-danger"></i>
                        4. Data Security
                    </h3>
                    <p>We implement appropriate security measures to protect your personal information:</p>
                    <ul>
                        <li>SSL encryption for data transmission</li>
                        <li>Secure servers and databases</li>
                        <li>Regular security audits and updates</li>
                        <li>Access controls and authentication</li>
                        <li>Employee training on data protection</li>
                        <li>Incident response procedures</li>
                    </ul>
                    <p>However, no method of transmission over the internet is 100% secure, and we cannot guarantee absolute security.</p>
                </div>

                <!-- Cookies -->
                <div id="cookies" class="policy-section">
                    <h3 class="fw-bold mb-3">
                        <i class="fas fa-cookie-bite me-2 text-info"></i>
                        5. Cookies and Tracking
                    </h3>
                    <p>We use cookies and similar tracking technologies to:</p>
                    <ul>
                        <li>Remember your preferences and settings</li>
                        <li>Analyze website traffic and usage</li>
                        <li>Provide personalized content</li>
                        <li>Improve website functionality</li>
                        <li>Enable social media features</li>
                    </ul>
                    <p>You can control cookies through your browser settings. However, disabling cookies may affect website functionality.</p>
                </div>

                <!-- User Rights -->
                <div id="user-rights" class="policy-section">
                    <h3 class="fw-bold mb-3">
                        <i class="fas fa-user-shield me-2 text-primary"></i>
                        6. Your Rights
                    </h3>
                    <p>You have the following rights regarding your personal information:</p>
                    <ul>
                        <li><strong>Access:</strong> Request access to your personal information</li>
                        <li><strong>Correction:</strong> Request correction of inaccurate information</li>
                        <li><strong>Deletion:</strong> Request deletion of your personal information</li>
                        <li><strong>Portability:</strong> Request transfer of your data</li>
                        <li><strong>Objection:</strong> Object to processing of your information</li>
                        <li><strong>Restriction:</strong> Request restriction of processing</li>
                        <li><strong>Withdrawal:</strong> Withdraw consent at any time</li>
                    </ul>
                    <p>To exercise these rights, please contact us using the information provided below.</p>
                </div>

                <!-- Data Retention -->
                <div id="data-retention" class="policy-section">
                    <h3 class="fw-bold mb-3">
                        <i class="fas fa-clock me-2 text-success"></i>
                        7. Data Retention
                    </h3>
                    <p>We retain your personal information for as long as necessary to:</p>
                    <ul>
                        <li>Provide our services to you</li>
                        <li>Comply with legal obligations</li>
                        <li>Resolve disputes and enforce agreements</li>
                        <li>Maintain business records</li>
                    </ul>
                    <p>When information is no longer needed, we will securely delete or anonymize it.</p>
                </div>

                <!-- Children's Privacy -->
                <div id="children-privacy" class="policy-section">
                    <h3 class="fw-bold mb-3">
                        <i class="fas fa-child me-2 text-warning"></i>
                        8. Children's Privacy
                    </h3>
                    <p>
                        Our services are not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13. If you are a parent or guardian and believe your child has provided us with personal information, please contact us immediately.
                    </p>
                </div>

                <!-- Policy Changes -->
                <div id="policy-changes" class="policy-section">
                    <h3 class="fw-bold mb-3">
                        <i class="fas fa-edit me-2 text-info"></i>
                        9. Policy Changes
                    </h3>
                    <p>
                        We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last updated" date. You are advised to review this Privacy Policy periodically for any changes.
                    </p>
                </div>

                <!-- Contact Information -->
                <div id="contact-info" class="policy-section">
                    <h3 class="fw-bold mb-3">
                        <i class="fas fa-envelope me-2 text-danger"></i>
                        10. Contact Information
                    </h3>
                    <p>If you have any questions about this Privacy Policy, please contact us:</p>
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card border-0 bg-light">
                                <div class="card-body">
                                    <h6 class="fw-bold">Email</h6>
                                    <p class="mb-0"><EMAIL></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-0 bg-light">
                                <div class="card-body">
                                    <h6 class="fw-bold">Phone</h6>
                                    <p class="mb-0">+237 651 408 682</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="card border-0 bg-light">
                            <div class="card-body">
                                <h6 class="fw-bold">Address</h6>
                                <p class="mb-0">ICT University, Yaoundé, Cameroon</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer Note -->
                <div class="text-center mt-5 pt-4 border-top">
                    <p class="text-muted">
                        <i class="fas fa-info-circle me-2"></i>
                        This Privacy Policy is effective as of <?php echo date('F j, Y'); ?> and applies to all users of ZARA-Events.
                    </p>
                    <a href="contact.php" class="btn btn-primary-modern">
                        <i class="fas fa-envelope me-2"></i>Contact Us for Questions
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-5" style="background: var(--dark-bg); color: white;">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-calendar-star me-2"></i>
                        ZARA-Events
                    </h5>
                    <p class="text-muted">Modern event booking platform for Central Africa. Discover, book, and experience amazing events with cutting-edge technology.</p>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="/" class="text-muted text-decoration-none">Home</a></li>
                        <li><a href="about.php" class="text-muted text-decoration-none">About</a></li>
                        <li><a href="contact.php" class="text-muted text-decoration-none">Contact</a></li>
                        <li><a href="help-center.php" class="text-muted text-decoration-none">Help Center</a></li>
                    </ul>
                </div>

                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Legal</h6>
                    <ul class="list-unstyled">
                        <li><a href="privacy-policy.php" class="text-muted text-decoration-none">Privacy Policy</a></li>
                        <li><a href="terms-of-service.php" class="text-muted text-decoration-none">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <hr class="my-4" style="border-color: rgba(255,255,255,0.1);">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 ZARA-Events. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">Developed by Tayong Fritz</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/modern-app.js"></script>
    
    <script>
        // Smooth scrolling for table of contents links
        document.querySelectorAll('.policy-toc a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
