#!/bin/bash

# ZARA-Events Railway Database Setup Script
# This script helps you set up Railway database for your application

echo "🚂 ZARA-Events Railway Database Setup"
echo "====================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if PHP is available
if ! command -v php &> /dev/null; then
    print_error "PHP is not installed or not in PATH"
    exit 1
fi

print_status "PHP is available"

# Check if required files exist
required_files=("includes/config.php" "database/railway-setup.sql" "test-railway-connection.php")
for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        print_error "Required file not found: $file"
        exit 1
    fi
done

print_status "All required files found"

# Set Railway environment variables
echo ""
print_info "Setting up Railway environment variables..."

export MYSQLHOST=mysql.railway.internal
export MYSQLPORT=3306
export MYSQLUSER=root
export MYSQLPASSWORD=DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU
export MYSQLDATABASE=railway

print_status "Environment variables set"

# Test Railway connection
echo ""
print_info "Testing Railway database connection..."

if php test-railway-connection.php; then
    print_status "Railway database connection successful!"
else
    print_error "Railway database connection failed!"
    echo ""
    print_info "Troubleshooting steps:"
    echo "1. Verify Railway MySQL service is running"
    echo "2. Check database credentials in Railway dashboard"
    echo "3. Ensure network connectivity to Railway"
    exit 1
fi

# Ask about data migration
echo ""
read -p "Do you want to migrate data from local Docker database? (y/n): " migrate_choice

if [[ $migrate_choice =~ ^[Yy]$ ]]; then
    print_info "Checking Docker database..."
    
    # Check if Docker is running
    if ! docker ps &> /dev/null; then
        print_warning "Docker is not running. Starting Docker services..."
        if command -v docker-compose &> /dev/null; then
            docker-compose up -d mysql
            sleep 10
        else
            print_error "Docker Compose not found. Please start Docker manually."
            exit 1
        fi
    fi
    
    print_info "Running data migration..."
    if php migrate-to-railway.php; then
        print_status "Data migration completed!"
    else
        print_warning "Data migration had issues. Check the output above."
    fi
else
    print_info "Skipping data migration"
fi

# Create environment file for deployment
echo ""
print_info "Creating environment configuration file..."

cat > .env.production << EOF
# Railway Production Environment Configuration
MYSQLHOST=mysql.railway.internal
MYSQLPORT=3306
MYSQLUSER=root
MYSQLPASSWORD=DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU
MYSQLDATABASE=railway

# Application Environment
ENVIRONMENT=production

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=pvjc rjit ogxg ncce
SMTP_ENCRYPTION=tls
FROM_EMAIL=<EMAIL>
FROM_NAME=ZARA-Events
EOF

print_status "Environment configuration created (.env.production)"

# Summary
echo ""
echo "🎉 Railway Database Setup Complete!"
echo "=================================="
echo ""
print_info "What was done:"
echo "  ✅ Updated database configuration to support Railway"
echo "  ✅ Tested Railway database connection"
echo "  ✅ Initialized database schema"
if [[ $migrate_choice =~ ^[Yy]$ ]]; then
    echo "  ✅ Migrated data from local database"
fi
echo "  ✅ Created production environment file"
echo ""

print_info "Next steps:"
echo "1. Deploy your application with Railway database credentials"
echo "2. Set environment variables in your deployment platform:"
echo "   - Copy variables from .env.production"
echo "   - Or use Railway's built-in environment variables"
echo ""
echo "3. Update SITE_URL in your deployment environment:"
echo "   SITE_URL=https://your-app-domain.com"
echo ""

print_info "Railway Database Credentials:"
echo "MYSQLHOST=mysql.railway.internal"
echo "MYSQLPORT=3306"
echo "MYSQLUSER=root"
echo "MYSQLPASSWORD=DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU"
echo "MYSQLDATABASE=railway"
echo ""

print_info "For detailed instructions, see: RAILWAY-DEPLOYMENT-GUIDE.md"

echo ""
print_status "Setup completed successfully! 🚀"
