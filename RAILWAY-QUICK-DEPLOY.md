# Railway Deployment Instructions for ZARA-Events

## 🚀 Quick Deployment Steps

### 1. Database Setup
Your Railway MySQL database is already configured with these credentials:
- Host: mysql.railway.internal
- Port: 3306
- User: root
- Password: DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU
- Database: railway

### 2. Initialize Database Schema
Upload and run the database/railway-setup.sql file in your Railway MySQL service:

**Option A: Using Railway Dashboard**
1. Go to your MySQL service in Railway
2. Click on 'Data' tab
3. Copy contents of database/railway-setup.sql
4. Paste and execute in the SQL editor

**Option B: Using MySQL Client (if TCP Proxy enabled)**
```bash
mysql -h [TCP_PROXY_HOST] -P [TCP_PROXY_PORT] -u root -p railway < database/railway-setup.sql
```

### 3. Deploy Web Application
1. Create a new Railway service for your web application
2. Connect your GitHub repository
3. Set environment variables from .env.production
4. Deploy!

### 4. Environment Variables to Set in Railway
Copy these to your Railway web service variables:

```
MYSQLHOST=mysql.railway.internal
MYSQLPORT=3306
MYSQLUSER=root
MYSQLPASSWORD=DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU
MYSQLDATABASE=railway
ENVIRONMENT=production
SITE_URL=https://your-app-name.railway.app
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=pvjc rjit ogxg ncce
SMTP_ENCRYPTION=tls
FROM_EMAIL=<EMAIL>
FROM_NAME=ZARA-Events
```

### 5. Verify Deployment
After deployment, your application will automatically:
- Connect to Railway MySQL database
- Use the existing schema and data
- Work with all existing features

## 📁 Files Ready for Railway
- ✅ includes/config.php (Railway-compatible)
- ✅ database/railway-setup.sql (Database schema)
- ✅ .env.production (Environment variables)
- ✅ All application files

## 🔧 Local Development
For local development, continue using Docker:
```bash
docker-compose up -d
```

Your application will automatically detect the environment and use appropriate database credentials.

## 🆘 Need Help?
- Railway Documentation: https://docs.railway.com
- Railway Discord: https://discord.gg/railway
- Project Documentation: RAILWAY-DEPLOYMENT-GUIDE.md
