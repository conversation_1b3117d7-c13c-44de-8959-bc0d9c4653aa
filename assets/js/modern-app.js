/**
 * ZARA-Events - Advanced JavaScript Framework
 * Features: Smooth animations, real-time updates, progressive enhancement, toast notifications
 */

class ModernEventApp {
    constructor() {
        this.init();
        this.setupEventListeners();
        this.initAnimations();
        this.setupProgressiveEnhancement();
    }

    init() {
        // Initialize app state
        this.loadCartCount();

        // Setup intersection observer for animations
        this.setupIntersectionObserver();

        // Initialize smooth scrolling
        this.initSmoothScrolling();

        // Setup real-time features
        this.initRealTimeFeatures();

        // Initialize toast container
        this.initToastContainer();
    }

    setupEventListeners() {
        // Navigation scroll effect
        window.addEventListener('scroll', this.handleNavbarScroll.bind(this));

        // Search functionality
        const searchInput = document.getElementById('eventSearch');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(this.handleSearch.bind(this), 300));
        }

        // Filter functionality
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(btn => {
            btn.addEventListener('click', this.handleFilter.bind(this));
        });

        // Add to cart buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('add-to-cart-btn')) {
                this.handleAddToCart(e);
            }
        });

        // Form enhancements
        this.enhanceForms();
    }

    handleNavbarScroll() {
        const navbar = document.querySelector('.navbar-modern');
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = 'none';
        }
    }

    setupIntersectionObserver() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe all animatable elements
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            this.observer.observe(el);
        });
    }

    initAnimations() {
        // Add CSS for animations
        const style = document.createElement('style');
        style.textContent = `
            .animate-on-scroll {
                opacity: 0;
                transform: translateY(30px);
                transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .animate-in {
                opacity: 1;
                transform: translateY(0);
            }

            .stagger-animation {
                animation-delay: calc(var(--stagger-delay, 0) * 0.1s);
            }

            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.05); }
            }

            .pulse-animation {
                animation: pulse 2s infinite;
            }
        `;
        document.head.appendChild(style);
    }

    initSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    handleSearch(e) {
        const query = e.target.value.toLowerCase();
        const eventCards = document.querySelectorAll('.event-card');

        eventCards.forEach(card => {
            const title = card.querySelector('.event-title').textContent.toLowerCase();
            const description = card.querySelector('.event-description')?.textContent.toLowerCase() || '';

            if (title.includes(query) || description.includes(query)) {
                card.style.display = 'block';
                card.classList.add('animate-in');
            } else {
                card.style.display = 'none';
            }
        });

        // Show "no results" message if needed
        this.toggleNoResultsMessage(query);
    }

    handleFilter(e) {
        const filterValue = e.target.dataset.filter;
        const eventCards = document.querySelectorAll('.event-card');

        // Update active filter button
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        e.target.classList.add('active');

        // Filter events
        eventCards.forEach((card, index) => {
            const category = card.dataset.category;

            if (filterValue === 'all' || category === filterValue) {
                card.style.display = 'block';
                // Stagger animation
                setTimeout(() => {
                    card.classList.add('animate-in');
                }, index * 100);
            } else {
                card.style.display = 'none';
                card.classList.remove('animate-in');
            }
        });
    }

    handleAddToCart(e) {
        e.preventDefault();
        const eventCard = e.target.closest('.event-card');
        const bookingForm = e.target.closest('.booking-form');

        let eventId, quantity = 1;

        if (eventCard) {
            eventId = eventCard.dataset.eventId;
        } else if (bookingForm) {
            eventId = bookingForm.dataset.eventId;
            const quantitySelect = bookingForm.querySelector('#quantity');
            if (quantitySelect) {
                quantity = parseInt(quantitySelect.value);
            }
        }

        if (!eventId) {
            this.showNotification('Error: Event not found', 'error');
            return;
        }

        // Add to cart with animation
        this.addToCartWithAnimation(eventId, quantity, e.target);
    }

    async addToCartWithAnimation(eventId, quantity, button) {
        // Prevent multiple clicks
        if (button.disabled) return;

        // Button animation
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Adding...';
        button.disabled = true;

        try {
            // Determine correct path based on current location
            const currentPath = window.location.pathname;
            const isInSubdirectory = currentPath.includes('/events/') || currentPath.includes('/admin/') || currentPath.includes('/user/');
            const cartPath = isInSubdirectory ? '../booking/add_to_cart.php' : 'booking/add_to_cart.php';

            const response = await fetch(cartPath, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    event_id: eventId,
                    quantity: quantity
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                // Update cart count
                this.updateCartCountDisplay(result.cart_count);

                // Flying cart animation
                this.createFlyingCartAnimation(button);

                // Success state
                button.innerHTML = '<i class="fas fa-check me-1"></i>Added!';
                button.classList.add('btn-success');

                // Show success notification (only one)
                this.showNotification(result.message, 'success');

                // Reset button after 2 seconds
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('btn-success');
                    button.disabled = false;
                }, 2000);

            } else {
                throw new Error(result.message || 'Failed to add to cart');
            }
        } catch (error) {
            console.error('Add to cart error:', error);
            button.innerHTML = originalText;
            button.disabled = false;

            // Show error notification (only one)
            if (error.message.includes('login') || error.message.includes('401')) {
                this.showNotification('Please login to add items to cart', 'error');
                setTimeout(() => {
                    window.location.href = '../auth/login.php';
                }, 2000);
            } else {
                this.showNotification(error.message || 'Failed to add to cart', 'error');
            }
        }
    }

    createFlyingCartAnimation(button) {
        const rect = button.getBoundingClientRect();
        const cartIcon = document.querySelector('.cart-icon');
        const cartRect = cartIcon.getBoundingClientRect();

        const flyingElement = document.createElement('div');
        flyingElement.innerHTML = '<i class="fas fa-ticket-alt"></i>';
        flyingElement.style.cssText = `
            position: fixed;
            left: ${rect.left}px;
            top: ${rect.top}px;
            z-index: 9999;
            color: #667eea;
            font-size: 20px;
            pointer-events: none;
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        `;

        document.body.appendChild(flyingElement);

        // Animate to cart
        setTimeout(() => {
            flyingElement.style.left = cartRect.left + 'px';
            flyingElement.style.top = cartRect.top + 'px';
            flyingElement.style.transform = 'scale(0.5)';
            flyingElement.style.opacity = '0';
        }, 100);

        // Remove element
        setTimeout(() => {
            document.body.removeChild(flyingElement);
        }, 900);
    }

    async loadCartCount() {
        try {
            // Determine correct path based on current location
            const currentPath = window.location.pathname;
            const isInSubdirectory = currentPath.includes('/events/') || currentPath.includes('/admin/') || currentPath.includes('/user/');
            const cartCountPath = isInSubdirectory ? '../booking/get_cart_count.php' : 'booking/get_cart_count.php';

            const response = await fetch(cartCountPath);
            const result = await response.json();

            if (result.success) {
                this.updateCartCountDisplay(result.cart_count);
            }
        } catch (error) {
            console.error('Failed to load cart count:', error);
        }
    }

    updateCartCountDisplay(count) {
        const cartCount = document.querySelector('.cart-count');
        if (cartCount) {
            cartCount.textContent = count;
            if (count > 0) {
                cartCount.style.display = 'inline-block';
                cartCount.classList.add('pulse-animation');
                setTimeout(() => {
                    cartCount.classList.remove('pulse-animation');
                }, 2000);
            } else {
                cartCount.style.display = 'none';
            }
        }
    }

    initToastContainer() {
        if (!document.querySelector('.toast-container')) {
            const container = document.createElement('div');
            container.className = 'toast-container';
            document.body.appendChild(container);
        }
    }

    showNotification(message, type = 'info') {
        // Use new toast system
        this.showToast(message, type);
    }

    showToast(message, type = 'info', duration = null) {
        const container = document.querySelector('.toast-container');
        if (!container) return;

        // Check for duplicate messages
        const existingToasts = container.querySelectorAll('.toast-modern');
        for (let existingToast of existingToasts) {
            const existingMessage = existingToast.querySelector('.toast-body').textContent;
            if (existingMessage === message) {
                // Don't show duplicate toast
                return existingToast;
            }
        }

        const toast = document.createElement('div');
        toast.className = `toast-modern ${type}`;

        let iconClass = 'info-circle';
        let title = 'Information';

        switch(type) {
            case 'success':
                iconClass = 'check-circle';
                title = 'Success';
                break;
            case 'error':
                iconClass = 'exclamation-circle';
                title = 'Error';
                break;
            case 'warning':
                iconClass = 'exclamation-triangle';
                title = 'Warning';
                break;
        }

        toast.innerHTML = `
            <div class="toast-header">
                <div class="toast-title">
                    <i class="fas fa-${iconClass} me-2"></i>
                    ${title}
                </div>
                <button class="toast-close" type="button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="toast-body">${message}</div>
        `;

        // Add close functionality
        toast.querySelector('.toast-close').addEventListener('click', () => {
            this.removeToast(toast);
        });

        container.appendChild(toast);

        // Show toast
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // Auto remove
        const autoRemoveDuration = duration || (type === 'error' ? 5000 : 3000);
        setTimeout(() => {
            this.removeToast(toast);
        }, autoRemoveDuration);

        return toast;
    }

    removeToast(toast) {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    enhanceForms() {
        // Add floating labels
        document.querySelectorAll('.form-control-modern').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        });

        // Real-time validation
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', this.handleFormSubmit.bind(this));
        });
    }

    handleFormSubmit(e) {
        const form = e.target;
        const submitBtn = form.querySelector('button[type="submit"]');

        if (submitBtn) {
            const originalText = submitBtn.textContent;
            submitBtn.innerHTML = '<div class="loading-spinner"></div> Processing...';
            submitBtn.disabled = true;

            // Reset after form processing (this would be handled by server response)
            setTimeout(() => {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }, 2000);
        }
    }

    initRealTimeFeatures() {
        // Simulate real-time ticket availability updates
        setInterval(() => {
            this.updateTicketAvailability();
        }, 30000); // Update every 30 seconds

        // Auto-save form data
        this.setupAutoSave();
    }

    updateTicketAvailability() {
        document.querySelectorAll('.ticket-count').forEach(element => {
            const currentCount = parseInt(element.textContent);
            // Simulate small changes in availability
            const change = Math.floor(Math.random() * 3) - 1; // -1, 0, or 1
            const newCount = Math.max(0, currentCount + change);

            if (newCount !== currentCount) {
                element.textContent = newCount;
                element.classList.add('pulse-animation');
                setTimeout(() => {
                    element.classList.remove('pulse-animation');
                }, 2000);
            }
        });
    }

    setupAutoSave() {
        document.querySelectorAll('input, textarea, select').forEach(input => {
            input.addEventListener('input', this.debounce(() => {
                const formData = new FormData(input.closest('form'));
                localStorage.setItem('formAutoSave', JSON.stringify(Object.fromEntries(formData)));
            }, 1000));
        });
    }

    setupProgressiveEnhancement() {
        // Add loading states
        document.querySelectorAll('img').forEach(img => {
            img.addEventListener('load', function() {
                this.classList.add('loaded');
            });
        });

        // Lazy loading for images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    toggleNoResultsMessage(query) {
        const visibleCards = document.querySelectorAll('.event-card[style*="block"]').length;
        let noResultsMsg = document.querySelector('.no-results-message');

        if (visibleCards === 0 && query) {
            if (!noResultsMsg) {
                noResultsMsg = document.createElement('div');
                noResultsMsg.className = 'no-results-message text-center py-5';
                noResultsMsg.innerHTML = `
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>No events found</h4>
                    <p class="text-muted">Try adjusting your search terms</p>
                `;
                document.querySelector('.events-container').appendChild(noResultsMsg);
            }
            noResultsMsg.style.display = 'block';
        } else if (noResultsMsg) {
            noResultsMsg.style.display = 'none';
        }
    }

    // Utility function for debouncing
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Global function to load cart count
window.loadCartCount = async function() {
    try {
        const response = await fetch('../booking/get_cart_count.php');
        const result = await response.json();

        if (result.success) {
            const cartCount = document.querySelector('.cart-count');
            if (cartCount) {
                cartCount.textContent = result.cart_count;
                if (result.cart_count > 0) {
                    cartCount.style.display = 'inline-block';
                } else {
                    cartCount.style.display = 'none';
                }
            }
        }
    } catch (error) {
        console.error('Failed to load cart count:', error);
    }
};

// Global toast function
window.showToast = function(message, type = 'info', duration = null) {
    if (window.modernEventApp) {
        return window.modernEventApp.showToast(message, type, duration);
    } else {
        // Fallback for when app isn't initialized yet
        console.log(`Toast: ${type.toUpperCase()} - ${message}`);
    }
};

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.modernEventApp = new ModernEventApp();
});

// Export for use in other modules
window.ModernEventApp = ModernEventApp;
