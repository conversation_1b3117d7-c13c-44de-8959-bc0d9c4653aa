<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'Search Events';

// Get search parameters
$searchTerm = $_GET['search'] ?? '';
$location = $_GET['location'] ?? '';
$date = $_GET['date'] ?? '';
$category = $_GET['category'] ?? '';
$sortBy = $_GET['sort'] ?? 'date';

// Perform search
$events = [];
if (!empty($searchTerm) || !empty($location) || !empty($date) || !empty($category)) {
    if (!empty($category)) {
        $events = $eventManager->getEventsByCategory($category);
    } else {
        $events = $eventManager->searchEvents($searchTerm, $location, $date);
    }

    // Apply additional filtering and sorting
    if (!empty($events)) {
        // Sort events
        switch ($sortBy) {
            case 'price_low':
                usort($events, function($a, $b) { return $a->price <=> $b->price; });
                break;
            case 'price_high':
                usort($events, function($a, $b) { return $b->price <=> $a->price; });
                break;
            case 'title':
                usort($events, function($a, $b) { return strcasecmp($a->title, $b->title); });
                break;
            default: // date
                usort($events, function($a, $b) { return strtotime($a->event_date) <=> strtotime($b->event_date); });
                break;
        }
    }
} else {
    // Show all events if no search criteria
    $events = $eventManager->getAllEvents();
}

// Get unique categories for filter
$db->query('SELECT DISTINCT category FROM events WHERE status = "active" ORDER BY category');
$categories = $db->resultset();

// Get unique locations for filter
$db->query('SELECT DISTINCT location FROM events WHERE status = "active" ORDER BY location');
$locations = $db->resultset();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="search.php">Search</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="../booking/cart.php">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle" style="display: none;">0</span>
                        </a>
                    </li>
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'User'); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="../user/dashboard.php">Dashboard</a></li>
                                <li><a class="dropdown-item" href="../user/profile.php">Profile</a></li>
                                <?php if (isAdmin()): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="../admin/">Admin Panel</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../auth/login.php">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary-modern ms-2" href="../auth/register.php">Sign Up</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Search Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card-modern">
                        <div class="card-body">
                            <h2 class="mb-4">
                                <i class="fas fa-search me-2"></i>
                                Search Events
                            </h2>

                            <!-- Search Form -->
                            <form method="GET" action="">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label for="search" class="form-label">Keywords</label>
                                        <input type="text" class="form-control form-control-modern" id="search"
                                               name="search" placeholder="Search events..."
                                               value="<?php echo htmlspecialchars($searchTerm); ?>">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="category" class="form-label">Category</label>
                                        <select class="form-control form-control-modern" id="category" name="category">
                                            <option value="">All Categories</option>
                                            <?php foreach ($categories as $cat): ?>
                                                <option value="<?php echo htmlspecialchars($cat->category); ?>"
                                                        <?php echo $category === $cat->category ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($cat->category); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="location" class="form-label">Location</label>
                                        <select class="form-control form-control-modern" id="location" name="location">
                                            <option value="">All Locations</option>
                                            <?php foreach ($locations as $loc): ?>
                                                <option value="<?php echo htmlspecialchars($loc->location); ?>"
                                                        <?php echo $location === $loc->location ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($loc->location); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="date" class="form-label">Date</label>
                                        <input type="date" class="form-control form-control-modern" id="date"
                                               name="date" value="<?php echo htmlspecialchars($date); ?>">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="sort" class="form-label">Sort By</label>
                                        <select class="form-control form-control-modern" id="sort" name="sort">
                                            <option value="date" <?php echo $sortBy === 'date' ? 'selected' : ''; ?>>Date</option>
                                            <option value="title" <?php echo $sortBy === 'title' ? 'selected' : ''; ?>>Title</option>
                                            <option value="price_low" <?php echo $sortBy === 'price_low' ? 'selected' : ''; ?>>Price: Low to High</option>
                                            <option value="price_high" <?php echo $sortBy === 'price_high' ? 'selected' : ''; ?>>Price: High to Low</option>
                                        </select>
                                    </div>
                                    <div class="col-md-1">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-primary-modern w-100">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Results -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4>
                            <?php if (!empty($searchTerm) || !empty($location) || !empty($date) || !empty($category)): ?>
                                Search Results (<?php echo count($events); ?> found)
                            <?php else: ?>
                                All Events (<?php echo count($events); ?> total)
                            <?php endif; ?>
                        </h4>

                        <?php if (!empty($searchTerm) || !empty($location) || !empty($date) || !empty($category)): ?>
                            <a href="search.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>Clear Filters
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Events Grid -->
            <div class="row">
                <?php if (!empty($events)): ?>
                    <?php foreach ($events as $event): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="event-card" data-event-id="<?php echo $event->id; ?>">
                                <div class="event-image-container position-relative">
                                    <img src="<?php echo $event->image_url ?: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'; ?>"
                                         alt="<?php echo htmlspecialchars($event->title); ?>"
                                         class="event-image">

                                    <div class="event-badge">
                                        <?php echo htmlspecialchars($event->category); ?>
                                    </div>

                                    <?php if ($event->available_tickets < 10): ?>
                                        <div class="position-absolute top-0 start-0 m-3">
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-fire me-1"></i>Almost Sold Out
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="event-content">
                                    <h5 class="event-title"><?php echo htmlspecialchars($event->title); ?></h5>

                                    <div class="event-meta mb-3">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-calendar-alt text-primary me-2"></i>
                                            <span><?php echo formatDate($event->event_date); ?></span>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-clock text-primary me-2"></i>
                                            <span><?php echo formatTime($event->event_time); ?></span>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                            <span><?php echo htmlspecialchars($event->venue . ', ' . $event->location); ?></span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-ticket-alt text-primary me-2"></i>
                                            <span class="ticket-count"><?php echo $event->available_tickets; ?></span>
                                            <span class="text-muted ms-1">tickets left</span>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="event-price">
                                            <?php echo formatCurrency($event->price); ?>
                                        </div>
                                        <div class="event-actions">
                                            <a href="details.php?id=<?php echo $event->id; ?>"
                                               class="btn btn-outline-primary btn-sm me-2">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-primary-modern btn-sm add-to-cart-btn">
                                                <i class="fas fa-cart-plus me-1"></i>
                                                Add to Cart
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4>No Events Found</h4>
                        <p class="text-muted">Try adjusting your search criteria or browse all events.</p>
                        <a href="./" class="btn btn-primary-modern">
                            <i class="fas fa-calendar-alt me-2"></i>Browse All Events
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <!-- Load cart count -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadCartCount();
        });
    </script>
</body>
</html>
