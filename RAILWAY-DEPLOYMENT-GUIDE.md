# Railway Database Deployment Guide

This guide will help you deploy your ZARA-Events database to Railway and configure your application to use it.

## 🎯 Overview

Railway provides managed MySQL databases with two access methods:
- **Internal access**: For applications deployed on Railway (`mysql.railway.internal`)
- **External access**: For local development and external connections (TCP Proxy)

## Prerequisites

1. Railway account (sign up at [railway.app](https://railway.app))
2. Local Docker environment running (for data migration)
3. Railway CLI installed (optional but recommended)

## Step 1: Create Railway MySQL Database

### Option A: Using Railway Dashboard
1. Go to [railway.app](https://railway.app) and log in
2. Create a new project or select existing project
3. Click "Add Service" → "Database" → "MySQL"
4. Railway will automatically provision a MySQL database

### Option B: Using Railway CLI
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login

# Create new project
railway new

# Add MySQL database
railway add mysql
```

## Step 2: Get Database Credentials

After creating the MySQL database:

1. Go to your Railway project dashboard
2. Click on the MySQL service
3. Go to "Variables" tab
4. Copy the following variables:
   - `MYSQLHOST` (usually: mysql.railway.internal)
   - `MYSQLPORT` (usually: 3306)
   - `MYSQLUSER` (usually: root)
   - `MYSQLPASSWORD` (auto-generated)
   - `MYSQLDATABASE` (usually: railway)

**Your provided credentials (Internal Railway Access):**
```
MYSQLHOST: mysql.railway.internal
MYSQLPORT: 3306
MYSQLUSER: root
MYSQLPASSWORD: DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU
MYSQLDATABASE: railway
```

## Step 2.1: Enable TCP Proxy for External Access (Optional)

If you want to connect to your Railway database from outside Railway (local development, external tools):

1. **Go to your MySQL service in Railway dashboard**
2. **Click on "Settings" tab**
3. **Scroll to "Public Networking" section**
4. **Click "Add TCP Proxy"**
5. **Enter port: 3306**
6. **Railway will generate external credentials like:**
   ```
   Host: your-project.railway.app
   Port: 12345 (example)
   ```

**Note**: External access incurs network egress charges. Use internal access for production deployments on Railway.

## Step 3: Test Railway Database Connection

1. **Set environment variables** (choose one method):

   **Method A: Export in terminal**
   ```bash
   export MYSQLHOST=mysql.railway.internal
   export MYSQLPORT=3306
   export MYSQLUSER=root
   export MYSQLPASSWORD=DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU
   export MYSQLDATABASE=railway
   ```

   **Method B: Create .env file**
   ```bash
   cp .env.railway .env
   # Edit .env with your actual credentials
   ```

2. **Test the connection:**
   ```bash
   php test-railway-connection.php
   ```

   This script will:
   - Test database connectivity
   - Initialize the database schema if needed
   - Verify all tables are created correctly

## Step 4: Migrate Your Data (Optional)

If you have existing data in your local Docker database:

1. **Ensure Docker is running:**
   ```bash
   docker-compose up -d mysql
   ```

2. **Run the migration script:**
   ```bash
   php migrate-to-railway.php
   ```

   This will:
   - Export data from your local Docker MySQL
   - Import it into Railway MySQL
   - Verify the migration was successful

## Step 5: Update Application Configuration

Your application is already configured to work with Railway! The `includes/config.php` file has been updated to automatically detect Railway environment variables.

**Priority order for database configuration:**
1. `DB_HOST`, `DB_USER`, `DB_PASS`, `DB_NAME` (if set)
2. `MYSQLHOST`, `MYSQLUSER`, `MYSQLPASSWORD`, `MYSQLDATABASE` (Railway variables)
3. Local Docker defaults (fallback)

## Step 6: Deploy to Railway

### Option A: Deploy Web Application to Railway

1. **Create a new Railway service for your web app:**
   ```bash
   railway add
   ```

2. **Set environment variables in Railway dashboard:**
   - Go to your web service → Variables
   - Add all variables from `.env.railway`

3. **Deploy from GitHub:**
   - Connect your GitHub repository
   - Railway will auto-deploy on git push

### Option B: Use with Existing Deployment

If you're using Google Cloud Run or another platform:

1. **Update environment variables** in your deployment platform
2. **Use the Railway database credentials**
3. **Redeploy your application**

## Step 7: Verify Deployment

1. **Check database connection:**
   ```bash
   # On your deployed app
   curl https://your-app.railway.app/test-railway-connection.php
   ```

2. **Test application functionality:**
   - User registration/login
   - Event browsing
   - Booking process
   - Admin panel

## Environment Variables Reference

### Required Railway Variables
```bash
MYSQLHOST=mysql.railway.internal
MYSQLPORT=3306
MYSQLUSER=root
MYSQLPASSWORD=DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU
MYSQLDATABASE=railway
```

### Optional Application Variables
```bash
ENVIRONMENT=production
SITE_URL=https://your-app.railway.app
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=pvjc rjit ogxg ncce
SMTP_ENCRYPTION=tls
FROM_EMAIL=<EMAIL>
FROM_NAME=ZARA-Events
```

## Troubleshooting

### Connection Issues
1. **Verify credentials** in Railway dashboard
2. **Check network connectivity** to Railway
3. **Ensure Railway MySQL service is running**

### Migration Issues
1. **Check Docker is running** for local database
2. **Verify local database has data** to migrate
3. **Check for duplicate key errors** (usually safe to ignore)

### Application Issues
1. **Clear application cache/sessions**
2. **Check error logs** in Railway dashboard
3. **Verify environment variables** are set correctly

## Security Notes

1. **Never commit credentials** to version control
2. **Use environment variables** for all sensitive data
3. **Regularly rotate database passwords**
4. **Monitor database access logs**

## Support

- Railway Documentation: [docs.railway.com](https://docs.railway.com)
- Railway Community: [Discord](https://discord.gg/railway)
- MySQL Documentation: [dev.mysql.com](https://dev.mysql.com/doc/)

---

**Next Steps:**
1. Run `php test-railway-connection.php` to verify setup
2. Optionally run `php migrate-to-railway.php` to migrate data
3. Deploy your application with Railway database credentials
4. Test all application functionality

Your ZARA-Events application is now ready to use Railway's managed MySQL database!
