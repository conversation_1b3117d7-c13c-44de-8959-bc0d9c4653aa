<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

$pageTitle = 'Help Center';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/modern-ui.css" rel="stylesheet">
    
    <style>
        .help-hero {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 120px 0 80px;
        }
        
        .help-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-top: -50px;
            position: relative;
            z-index: 2;
        }
        
        .faq-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .faq-header {
            background: var(--light-bg);
            padding: 20px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .faq-header:hover {
            background: #e9ecef;
        }
        
        .faq-content {
            padding: 20px;
            display: none;
        }
        
        .faq-content.show {
            display: block;
        }
        
        .category-card {
            border: none;
            border-radius: 15px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
        
        .search-box {
            background: white;
            border-radius: 50px;
            padding: 15px 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events/">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events/search.php">Search</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">Contact</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="booking/cart.php">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle" style="display: none;">0</span>
                        </a>
                    </li>
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'User'); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="user/dashboard.php">Dashboard</a></li>
                                <li><a class="dropdown-item" href="user/profile.php">Profile</a></li>
                                <?php if (isAdmin()): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="admin/">Admin Panel</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php">Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary-modern ms-2" href="auth/register.php">Sign Up</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Help Hero Section -->
    <section class="help-hero">
        <div class="container text-center">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">Help Center</h1>
                    <p class="lead mb-4">Find answers to your questions and get the help you need</p>
                    
                    <div class="search-box d-inline-flex align-items-center">
                        <i class="fas fa-search text-muted me-3"></i>
                        <input type="text" class="form-control border-0" placeholder="Search for help..." id="helpSearch">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Help Categories -->
    <section class="py-5">
        <div class="container">
            <div class="help-card">
                <div class="row mb-5">
                    <div class="col-lg-12 text-center">
                        <h2 class="fw-bold mb-4">How can we help you?</h2>
                        <p class="text-muted">Choose a category below or browse our frequently asked questions</p>
                    </div>
                </div>
                
                <div class="row mb-5">
                    <div class="col-md-4 mb-4">
                        <div class="card category-card shadow-sm h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-ticket-alt fa-3x text-primary mb-3"></i>
                                <h5 class="fw-bold">Booking Events</h5>
                                <p class="text-muted">Learn how to search, book, and manage your event tickets</p>
                                <a href="#booking-faq" class="btn btn-outline-primary">View FAQs</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-4">
                        <div class="card category-card shadow-sm h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-credit-card fa-3x text-success mb-3"></i>
                                <h5 class="fw-bold">Payments</h5>
                                <p class="text-muted">Information about payment methods, refunds, and billing</p>
                                <a href="#payment-faq" class="btn btn-outline-success">View FAQs</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-4">
                        <div class="card category-card shadow-sm h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-user-circle fa-3x text-warning mb-3"></i>
                                <h5 class="fw-bold">Account</h5>
                                <p class="text-muted">Manage your profile, password, and account settings</p>
                                <a href="#account-faq" class="btn btn-outline-warning">View FAQs</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Booking FAQs -->
                <div id="booking-faq" class="mb-5">
                    <h3 class="fw-bold mb-4">
                        <i class="fas fa-ticket-alt me-2 text-primary"></i>
                        Booking Events
                    </h3>
                    
                    <div class="faq-item">
                        <div class="faq-header" onclick="toggleFaq(this)">
                            <h6 class="mb-0 fw-bold">
                                How do I book an event?
                                <i class="fas fa-chevron-down float-end"></i>
                            </h6>
                        </div>
                        <div class="faq-content">
                            <p>To book an event:</p>
                            <ol>
                                <li>Browse events on our homepage or search page</li>
                                <li>Click on an event to view details</li>
                                <li>Select the number of tickets you want</li>
                                <li>Add to cart and proceed to checkout</li>
                                <li>Fill in your details and complete payment</li>
                                <li>You'll receive a confirmation email with your ticket</li>
                            </ol>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-header" onclick="toggleFaq(this)">
                            <h6 class="mb-0 fw-bold">
                                Can I cancel my booking?
                                <i class="fas fa-chevron-down float-end"></i>
                            </h6>
                        </div>
                        <div class="faq-content">
                            <p>Yes, you can cancel your booking up to 24 hours before the event. To cancel:</p>
                            <ul>
                                <li>Go to your dashboard</li>
                                <li>Find the booking you want to cancel</li>
                                <li>Click "Cancel Booking"</li>
                                <li>Refunds will be processed within 3-5 business days</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-header" onclick="toggleFaq(this)">
                            <h6 class="mb-0 fw-bold">
                                Where can I find my tickets?
                                <i class="fas fa-chevron-down float-end"></i>
                            </h6>
                        </div>
                        <div class="faq-content">
                            <p>Your tickets are available in multiple places:</p>
                            <ul>
                                <li>In your confirmation email</li>
                                <li>In your user dashboard under "My Bookings"</li>
                                <li>You can download them as PDF or view the QR code</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Payment FAQs -->
                <div id="payment-faq" class="mb-5">
                    <h3 class="fw-bold mb-4">
                        <i class="fas fa-credit-card me-2 text-success"></i>
                        Payments
                    </h3>
                    
                    <div class="faq-item">
                        <div class="faq-header" onclick="toggleFaq(this)">
                            <h6 class="mb-0 fw-bold">
                                What payment methods do you accept?
                                <i class="fas fa-chevron-down float-end"></i>
                            </h6>
                        </div>
                        <div class="faq-content">
                            <p>We accept the following payment methods:</p>
                            <ul>
                                <li>Credit/Debit Cards (Visa, Mastercard)</li>
                                <li>Mobile Money (MTN, Orange)</li>
                                <li>Bank Transfer</li>
                                <li>PayPal (for international payments)</li>
                            </ul>
                            <p>All payments are processed securely and your information is protected.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-header" onclick="toggleFaq(this)">
                            <h6 class="mb-0 fw-bold">
                                How do refunds work?
                                <i class="fas fa-chevron-down float-end"></i>
                            </h6>
                        </div>
                        <div class="faq-content">
                            <p>Refund policy:</p>
                            <ul>
                                <li>Full refund if cancelled 24+ hours before event</li>
                                <li>50% refund if cancelled 12-24 hours before event</li>
                                <li>No refund if cancelled less than 12 hours before event</li>
                                <li>Refunds are processed within 3-5 business days</li>
                                <li>Event cancellations by organizers result in full refunds</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Account FAQs -->
                <div id="account-faq" class="mb-5">
                    <h3 class="fw-bold mb-4">
                        <i class="fas fa-user-circle me-2 text-warning"></i>
                        Account Management
                    </h3>
                    
                    <div class="faq-item">
                        <div class="faq-header" onclick="toggleFaq(this)">
                            <h6 class="mb-0 fw-bold">
                                How do I reset my password?
                                <i class="fas fa-chevron-down float-end"></i>
                            </h6>
                        </div>
                        <div class="faq-content">
                            <p>To reset your password:</p>
                            <ol>
                                <li>Go to the login page</li>
                                <li>Click "Forgot Password?"</li>
                                <li>Enter your email address</li>
                                <li>Check your email for reset instructions</li>
                                <li>Follow the link to create a new password</li>
                            </ol>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-header" onclick="toggleFaq(this)">
                            <h6 class="mb-0 fw-bold">
                                How do I update my profile?
                                <i class="fas fa-chevron-down float-end"></i>
                            </h6>
                        </div>
                        <div class="faq-content">
                            <p>To update your profile:</p>
                            <ol>
                                <li>Log in to your account</li>
                                <li>Go to your dashboard</li>
                                <li>Click on "Profile" in the menu</li>
                                <li>Update your information</li>
                                <li>Click "Save Changes"</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <!-- Contact Section -->
                <div class="text-center mt-5 pt-5 border-top">
                    <h4 class="fw-bold mb-3">Still need help?</h4>
                    <p class="text-muted mb-4">Can't find what you're looking for? Our support team is here to help!</p>
                    
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <a href="contact.php" class="btn btn-primary-modern btn-lg w-100">
                                        <i class="fas fa-envelope me-2"></i>Contact Support
                                    </a>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <a href="https://wa.me/237651408682" class="btn btn-success btn-lg w-100" target="_blank">
                                        <i class="fab fa-whatsapp me-2"></i>WhatsApp Chat
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-5" style="background: var(--dark-bg); color: white;">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-calendar-star me-2"></i>
                        ZARA-Events
                    </h5>
                    <p class="text-muted">Modern event booking platform for Central Africa. Discover, book, and experience amazing events with cutting-edge technology.</p>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="/" class="text-muted text-decoration-none">Home</a></li>
                        <li><a href="about.php" class="text-muted text-decoration-none">About</a></li>
                        <li><a href="contact.php" class="text-muted text-decoration-none">Contact</a></li>
                        <li><a href="help-center.php" class="text-muted text-decoration-none">Help Center</a></li>
                    </ul>
                </div>

                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Legal</h6>
                    <ul class="list-unstyled">
                        <li><a href="privacy-policy.php" class="text-muted text-decoration-none">Privacy Policy</a></li>
                        <li><a href="terms-of-service.php" class="text-muted text-decoration-none">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <hr class="my-4" style="border-color: rgba(255,255,255,0.1);">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 ZARA-Events. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">Developed by Tayong Fritz</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/modern-app.js"></script>
    
    <script>
        function toggleFaq(element) {
            const content = element.nextElementSibling;
            const icon = element.querySelector('.fa-chevron-down');
            
            if (content.classList.contains('show')) {
                content.classList.remove('show');
                icon.style.transform = 'rotate(0deg)';
            } else {
                // Close all other FAQs
                document.querySelectorAll('.faq-content.show').forEach(item => {
                    item.classList.remove('show');
                });
                document.querySelectorAll('.fa-chevron-down').forEach(icon => {
                    icon.style.transform = 'rotate(0deg)';
                });
                
                // Open clicked FAQ
                content.classList.add('show');
                icon.style.transform = 'rotate(180deg)';
            }
        }
        
        // Search functionality
        document.getElementById('helpSearch').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const faqItems = document.querySelectorAll('.faq-item');
            
            faqItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
