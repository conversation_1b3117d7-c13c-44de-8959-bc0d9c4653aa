<?php
/**
 * Simple Railway Database Setup Script
 * This script prepares your application for Railway deployment
 * without requiring external database access for testing.
 */

echo "🚂 ZARA-Events Railway Setup (Simple Mode)\n";
echo "==========================================\n\n";

// Check if required files exist
$requiredFiles = [
    'includes/config.php',
    'database/railway-setup.sql',
    '.env.railway'
];

echo "✅ Checking required files...\n";
foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "  ✅ $file - Found\n";
    } else {
        echo "  ❌ $file - Missing\n";
        exit(1);
    }
}

echo "\n✅ All required files found!\n\n";

// Verify config.php has Railway support
echo "✅ Checking Railway configuration support...\n";
$configContent = file_get_contents('includes/config.php');

if (strpos($configContent, 'MYSQLHOST') !== false && 
    strpos($configContent, 'MYSQLUSER') !== false) {
    echo "  ✅ Railway environment variables supported\n";
} else {
    echo "  ❌ Railway support not found in config.php\n";
    exit(1);
}

echo "\n✅ Configuration is Railway-ready!\n\n";

// Create production environment file
echo "✅ Creating production environment configuration...\n";

$envContent = "# Railway Production Environment Configuration
# Set these variables in your Railway service

# Railway MySQL Database (Internal Access)
MYSQLHOST=mysql.railway.internal
MYSQLPORT=3306
MYSQLUSER=root
MYSQLPASSWORD=DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU
MYSQLDATABASE=railway

# Application Environment
ENVIRONMENT=production

# Site Configuration (update with your Railway app URL)
SITE_URL=https://your-app-name.railway.app

# Email Configuration (Gmail SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=pvjc rjit ogxg ncce
SMTP_ENCRYPTION=tls
FROM_EMAIL=<EMAIL>
FROM_NAME=ZARA-Events
";

file_put_contents('.env.production', $envContent);
echo "  ✅ Created .env.production\n";

// Create Railway deployment instructions
echo "\n✅ Creating Railway deployment instructions...\n";

$instructions = "# Railway Deployment Instructions for ZARA-Events

## 🚀 Quick Deployment Steps

### 1. Database Setup
Your Railway MySQL database is already configured with these credentials:
- Host: mysql.railway.internal
- Port: 3306
- User: root
- Password: DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU
- Database: railway

### 2. Initialize Database Schema
Upload and run the database/railway-setup.sql file in your Railway MySQL service:

**Option A: Using Railway Dashboard**
1. Go to your MySQL service in Railway
2. Click on 'Data' tab
3. Copy contents of database/railway-setup.sql
4. Paste and execute in the SQL editor

**Option B: Using MySQL Client (if TCP Proxy enabled)**
```bash
mysql -h [TCP_PROXY_HOST] -P [TCP_PROXY_PORT] -u root -p railway < database/railway-setup.sql
```

### 3. Deploy Web Application
1. Create a new Railway service for your web application
2. Connect your GitHub repository
3. Set environment variables from .env.production
4. Deploy!

### 4. Environment Variables to Set in Railway
Copy these to your Railway web service variables:

```
MYSQLHOST=mysql.railway.internal
MYSQLPORT=3306
MYSQLUSER=root
MYSQLPASSWORD=DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU
MYSQLDATABASE=railway
ENVIRONMENT=production
SITE_URL=https://your-app-name.railway.app
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=pvjc rjit ogxg ncce
SMTP_ENCRYPTION=tls
FROM_EMAIL=<EMAIL>
FROM_NAME=ZARA-Events
```

### 5. Verify Deployment
After deployment, your application will automatically:
- Connect to Railway MySQL database
- Use the existing schema and data
- Work with all existing features

## 📁 Files Ready for Railway
- ✅ includes/config.php (Railway-compatible)
- ✅ database/railway-setup.sql (Database schema)
- ✅ .env.production (Environment variables)
- ✅ All application files

## 🔧 Local Development
For local development, continue using Docker:
```bash
docker-compose up -d
```

Your application will automatically detect the environment and use appropriate database credentials.

## 🆘 Need Help?
- Railway Documentation: https://docs.railway.com
- Railway Discord: https://discord.gg/railway
- Project Documentation: RAILWAY-DEPLOYMENT-GUIDE.md
";

file_put_contents('RAILWAY-QUICK-DEPLOY.md', $instructions);
echo "  ✅ Created RAILWAY-QUICK-DEPLOY.md\n";

// Summary
echo "\n🎉 Railway Setup Complete!\n";
echo "========================\n\n";

echo "📋 What was prepared:\n";
echo "  ✅ Database configuration updated for Railway\n";
echo "  ✅ Production environment file created\n";
echo "  ✅ Quick deployment guide created\n";
echo "  ✅ Database schema ready for Railway\n\n";

echo "🚀 Next Steps:\n";
echo "1. Go to Railway dashboard and create MySQL service\n";
echo "2. Initialize database using database/railway-setup.sql\n";
echo "3. Create web service and connect your repository\n";
echo "4. Set environment variables from .env.production\n";
echo "5. Deploy and enjoy!\n\n";

echo "📖 Detailed instructions: RAILWAY-QUICK-DEPLOY.md\n";
echo "📖 Complete guide: RAILWAY-DEPLOYMENT-GUIDE.md\n\n";

echo "✨ Your ZARA-Events application is ready for Railway! ✨\n";

?>
