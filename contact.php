<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/email.php';

$pageTitle = 'Contact Us';
$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitizeInput($_POST['name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $subject = sanitizeInput($_POST['subject'] ?? '');
    $messageText = sanitizeInput($_POST['message'] ?? '');
    $category = sanitizeInput($_POST['category'] ?? '');
    
    // Validation
    if (empty($name) || empty($email) || empty($subject) || empty($messageText)) {
        $message = 'Please fill in all required fields.';
        $messageType = 'error';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = 'Please enter a valid email address.';
        $messageType = 'error';
    } else {
        // Send email
        global $emailManager;
        $fullSubject = "[$category] $subject";
        
        if ($emailManager->sendContactMessage($name, $email, $fullSubject, $messageText)) {
            $message = 'Thank you for your message! We will get back to you soon.';
            $messageType = 'success';
            
            // Clear form data on success
            $name = $email = $subject = $messageText = $category = '';
        } else {
            $message = 'Sorry, there was an error sending your message. Please try again or contact us directly.';
            $messageType = 'error';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/modern-ui.css" rel="stylesheet">
    
    <style>
        .contact-hero {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 120px 0 80px;
        }
        
        .contact-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-top: -50px;
            position: relative;
            z-index: 2;
        }
        
        .contact-info-card {
            background: var(--light-bg);
            border-radius: 15px;
            padding: 30px;
            height: 100%;
            border-left: 5px solid var(--primary-color);
        }
        
        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            color: white;
            text-decoration: none;
            margin: 0 10px 10px 0;
            transition: transform 0.3s ease;
        }
        
        .social-link:hover {
            transform: translateY(-3px);
            color: white;
        }
        
        .social-link.facebook { background: #1877f2; }
        .social-link.twitter { background: #1da1f2; }
        .social-link.instagram { background: #e4405f; }
        .social-link.linkedin { background: #0077b5; }
        .social-link.whatsapp { background: #25d366; }
        .social-link.telegram { background: #0088cc; }
        
        .floating-contact {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }
        
        .floating-contact .btn {
            border-radius: 50px;
            padding: 15px 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events/">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events/search.php">Search</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="contact.php">Contact</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="booking/cart.php">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle" style="display: none;">0</span>
                        </a>
                    </li>
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'User'); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="user/dashboard.php">Dashboard</a></li>
                                <li><a class="dropdown-item" href="user/profile.php">Profile</a></li>
                                <?php if (isAdmin()): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="admin/">Admin Panel</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php">Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary-modern ms-2" href="auth/register.php">Sign Up</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Contact Hero Section -->
    <section class="contact-hero">
        <div class="container text-center">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">Get In Touch</h1>
                    <p class="lead">Have questions about our events or need assistance? We're here to help!</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form Section -->
    <section class="py-5">
        <div class="container">
            <div class="contact-card">
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-lg-8">
                        <h2 class="fw-bold mb-4">Send us a Message</h2>
                        
                        <form method="POST" action="">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="category" class="form-label">Category</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="General Inquiry" <?php echo ($category ?? '') === 'General Inquiry' ? 'selected' : ''; ?>>General Inquiry</option>
                                        <option value="Event Booking" <?php echo ($category ?? '') === 'Event Booking' ? 'selected' : ''; ?>>Event Booking</option>
                                        <option value="Technical Support" <?php echo ($category ?? '') === 'Technical Support' ? 'selected' : ''; ?>>Technical Support</option>
                                        <option value="Partnership" <?php echo ($category ?? '') === 'Partnership' ? 'selected' : ''; ?>>Partnership</option>
                                        <option value="Feedback" <?php echo ($category ?? '') === 'Feedback' ? 'selected' : ''; ?>>Feedback</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label">Subject *</label>
                                    <input type="text" class="form-control" id="subject" name="subject" value="<?php echo htmlspecialchars($subject ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">Message *</label>
                                <textarea class="form-control" id="message" name="message" rows="6" required><?php echo htmlspecialchars($messageText ?? ''); ?></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary-modern btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>Send Message
                            </button>
                        </form>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="contact-info-card">
                            <h4 class="fw-bold mb-4">Contact Information</h4>
                            
                            <div class="mb-4">
                                <h6 class="fw-bold">
                                    <i class="fas fa-envelope me-2 text-primary"></i>Email
                                </h6>
                                <p class="text-muted"><EMAIL></p>
                            </div>
                            
                            <div class="mb-4">
                                <h6 class="fw-bold">
                                    <i class="fas fa-phone me-2 text-primary"></i>Phone
                                </h6>
                                <p class="text-muted">+237 651 408 682</p>
                            </div>
                            
                            <div class="mb-4">
                                <h6 class="fw-bold">
                                    <i class="fas fa-map-marker-alt me-2 text-primary"></i>Location
                                </h6>
                                <p class="text-muted">Yaoundé, Cameroon</p>
                            </div>
                            
                            <div class="mb-4">
                                <h6 class="fw-bold">
                                    <i class="fas fa-clock me-2 text-primary"></i>Response Time
                                </h6>
                                <p class="text-muted">Within 24 hours</p>
                            </div>
                            
                            <div>
                                <h6 class="fw-bold mb-3">Follow Us</h6>
                                <div>
                                    <a href="https://wa.me/237651408682" class="social-link whatsapp" target="_blank" title="WhatsApp">
                                        <i class="fab fa-whatsapp"></i>
                                    </a>
                                    <a href="https://facebook.com/tayongfritz" class="social-link facebook" target="_blank" title="Facebook">
                                        <i class="fab fa-facebook-f"></i>
                                    </a>
                                    <a href="https://twitter.com/tayongfritz" class="social-link twitter" target="_blank" title="Twitter">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                    <a href="https://instagram.com/tayongfritz" class="social-link instagram" target="_blank" title="Instagram">
                                        <i class="fab fa-instagram"></i>
                                    </a>
                                    <a href="https://linkedin.com/in/tayongfritz" class="social-link linkedin" target="_blank" title="LinkedIn">
                                        <i class="fab fa-linkedin-in"></i>
                                    </a>
                                    <a href="https://t.me/tayongfritz" class="social-link telegram" target="_blank" title="Telegram">
                                        <i class="fab fa-telegram-plane"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Contact Options -->
    <section class="py-5" style="background: var(--light-bg);">
        <div class="container">
            <div class="row text-center">
                <div class="col-lg-12 mb-4">
                    <h3 class="fw-bold">Quick Contact Options</h3>
                    <p class="text-muted">Choose your preferred way to reach us</p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3 mb-4">
                    <div class="card border-0 shadow-sm h-100 text-center">
                        <div class="card-body">
                            <i class="fab fa-whatsapp fa-3x text-success mb-3"></i>
                            <h5 class="fw-bold">WhatsApp</h5>
                            <p class="text-muted">Quick messaging</p>
                            <a href="https://wa.me/237651408682" class="btn btn-success" target="_blank">
                                <i class="fab fa-whatsapp me-2"></i>Chat Now
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-4">
                    <div class="card border-0 shadow-sm h-100 text-center">
                        <div class="card-body">
                            <i class="fas fa-envelope fa-3x text-primary mb-3"></i>
                            <h5 class="fw-bold">Email</h5>
                            <p class="text-muted">Detailed inquiries</p>
                            <a href="mailto:<EMAIL>" class="btn btn-primary">
                                <i class="fas fa-envelope me-2"></i>Send Email
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-4">
                    <div class="card border-0 shadow-sm h-100 text-center">
                        <div class="card-body">
                            <i class="fas fa-phone fa-3x text-warning mb-3"></i>
                            <h5 class="fw-bold">Phone</h5>
                            <p class="text-muted">Direct conversation</p>
                            <a href="tel:+237651408682" class="btn btn-warning">
                                <i class="fas fa-phone me-2"></i>Call Now
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-4">
                    <div class="card border-0 shadow-sm h-100 text-center">
                        <div class="card-body">
                            <i class="fab fa-telegram-plane fa-3x text-info mb-3"></i>
                            <h5 class="fw-bold">Telegram</h5>
                            <p class="text-muted">Instant messaging</p>
                            <a href="https://t.me/tayongfritz" class="btn btn-info" target="_blank">
                                <i class="fab fa-telegram-plane me-2"></i>Message
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Floating Contact Button -->
    <div class="floating-contact">
        <a href="https://wa.me/237651408682" class="btn btn-success" target="_blank" title="Contact us on WhatsApp">
            <i class="fab fa-whatsapp me-2"></i>Contact Us
        </a>
    </div>

    <!-- Footer -->
    <footer class="py-5" style="background: var(--dark-bg); color: white;">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-calendar-star me-2"></i>
                        ZARA-Events
                    </h5>
                    <p class="text-muted">Modern event booking platform for Central Africa. Discover, book, and experience amazing events with cutting-edge technology.</p>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="/" class="text-muted text-decoration-none">Home</a></li>
                        <li><a href="about.php" class="text-muted text-decoration-none">About</a></li>
                        <li><a href="contact.php" class="text-muted text-decoration-none">Contact</a></li>
                        <li><a href="help-center.php" class="text-muted text-decoration-none">Help Center</a></li>
                    </ul>
                </div>

                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Legal</h6>
                    <ul class="list-unstyled">
                        <li><a href="privacy-policy.php" class="text-muted text-decoration-none">Privacy Policy</a></li>
                        <li><a href="terms-of-service.php" class="text-muted text-decoration-none">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <hr class="my-4" style="border-color: rgba(255,255,255,0.1);">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 ZARA-Events. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">Developed by Tayong Fritz</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/modern-app.js"></script>
</body>
</html>
