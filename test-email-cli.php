<?php
require_once 'includes/config.php';
require_once 'includes/email.php';

echo "=== ZARA-Events Email Testing CLI ===\n\n";

// Display current configuration
echo "Current Email Configuration:\n";
echo "SMTP Host: " . SMTP_HOST . "\n";
echo "SMTP Port: " . SMTP_PORT . "\n";
echo "SMTP Username: " . SMTP_USERNAME . "\n";
echo "SMTP Encryption: " . SMTP_ENCRYPTION . "\n";
echo "From Email: " . FROM_EMAIL . "\n";
echo "From Name: " . FROM_NAME . "\n";
echo "Admin Email: " . ADMIN_EMAIL . "\n\n";

// Test 1: Basic test email
echo "Test 1: Sending basic test email...\n";
global $emailManager;

try {
    if ($emailManager->sendTestEmail()) {
        echo "✅ Basic test email sent successfully!\n";
    } else {
        echo "❌ Failed to send basic test email.\n";
    }
} catch (Exception $e) {
    echo "❌ Error sending basic test email: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Contact form test
echo "Test 2: Sending contact form test email...\n";

try {
    if ($emailManager->sendContactMessage(
        'Test User CLI',
        '<EMAIL>',
        'CLI Test Contact Message',
        'This is a test contact message sent from the CLI to verify the contact form functionality is working correctly.'
    )) {
        echo "✅ Contact form test email sent successfully!\n";
    } else {
        echo "❌ Failed to send contact form test email.\n";
    }
} catch (Exception $e) {
    echo "❌ Error sending contact form test email: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Custom email using sendEmail function
echo "Test 3: Sending custom email using sendEmail function...\n";

try {
    $customSubject = 'CLI Custom Test Email - ZARA-Events';
    $customBody = '
    <h2>🎉 CLI Email Test Successful!</h2>
    <p>This is a custom test email sent from the CLI.</p>
    <p><strong>Test Details:</strong></p>
    <ul>
        <li>Sent from: CLI Test Script</li>
        <li>Timestamp: ' . date('Y-m-d H:i:s') . '</li>
        <li>Platform: ZARA-Events</li>
        <li>Developer: Tayong Fritz</li>
    </ul>
    <p>If you received this email, the email system is working correctly!</p>
    <hr>
    <p><small>Sent from ZARA-Events CLI Test</small></p>
    ';
    
    if (sendEmail(ADMIN_EMAIL, 'Admin', $customSubject, $customBody)) {
        echo "✅ Custom email sent successfully!\n";
    } else {
        echo "❌ Failed to send custom email.\n";
    }
} catch (Exception $e) {
    echo "❌ Error sending custom email: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Test with different recipient (if provided as argument)
if (isset($argv[1]) && filter_var($argv[1], FILTER_VALIDATE_EMAIL)) {
    $testEmail = $argv[1];
    echo "Test 4: Sending test email to custom recipient ($testEmail)...\n";
    
    try {
        if ($emailManager->sendTestEmail($testEmail)) {
            echo "✅ Test email sent to $testEmail successfully!\n";
        } else {
            echo "❌ Failed to send test email to $testEmail.\n";
        }
    } catch (Exception $e) {
        echo "❌ Error sending test email to $testEmail: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

echo "=== Email Testing Complete ===\n";
echo "Check your email inbox at " . ADMIN_EMAIL . " for the test emails.\n";

if (!isset($argv[1])) {
    echo "\nTip: You can test with a custom email by running:\n";
    echo "php test-email-cli.php <EMAIL>\n";
}

echo "\nFor web-based testing, visit: http://localhost:9000/test-email.php\n";
echo "For contact form testing, visit: http://localhost:9000/contact.php\n";
?>
