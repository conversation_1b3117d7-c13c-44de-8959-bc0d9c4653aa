version: '3.8'

services:
  # ZARA-Events Web Application
  web:
    image: zaramillion/zara-events:latest
    container_name: zara_events_web
    restart: unless-stopped
    ports:
      - "9000:80"
    environment:
      - DB_HOST=mysql:3306
      - DB_USER=event_user
      - DB_PASS=event_password
      - DB_NAME=event_booking_system
      - ENVIRONMENT=production
      - SMTP_HOST=smtp.gmail.com
      - SMTP_PORT=587
      - SMTP_USERNAME=<EMAIL>
      - SMTP_PASSWORD=pvjc rjit ogxg ncce
      - SMTP_ENCRYPTION=tls
      - FROM_EMAIL=<EMAIL>
      - FROM_NAME=ZARA-Events
      - ADMIN_EMAIL=<EMAIL>
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - event_booking_network
    volumes:
      - web_uploads:/var/www/html/assets/images
      - web_logs:/var/www/html/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL Database Service
  mysql:
    image: mysql:8.0
    container_name: zara_events_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: zara_root_password_2024
      MYSQL_DATABASE: event_booking_system
      MYSQL_USER: event_user
      MYSQL_PASSWORD: event_password
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - event_booking_network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "event_user", "-pevent_password"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # phpMyAdmin for Database Management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: zara_events_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: event_user
      PMA_PASSWORD: event_password
      PMA_ARBITRARY: 1
      UPLOAD_LIMIT: 100M
    ports:
      - "8081:80"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - event_booking_network

  # Redis for Session Storage and Caching (Optional)
  redis:
    image: redis:7-alpine
    container_name: zara_events_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - event_booking_network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (Optional for SSL termination)
  nginx:
    image: nginx:alpine
    container_name: zara_events_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - web_logs:/var/log/nginx
    depends_on:
      - web
    networks:
      - event_booking_network
    profiles:
      - production

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  web_uploads:
    driver: local
  web_logs:
    driver: local

networks:
  event_booking_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
