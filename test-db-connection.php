<?php
require_once 'includes/config.php';

echo "<h2>ZARA-Events Database Connection Test</h2>";

try {
    // Test basic connection
    echo "<h3>✅ Database Connection: SUCCESS</h3>";
    echo "<p>Connected to database: <strong>" . DB_NAME . "</strong></p>";
    echo "<p>Host: <strong>" . DB_HOST . "</strong></p>";
    echo "<p>User: <strong>" . DB_USER . "</strong></p>";

    // Test tables exist
    echo "<h3>Database Tables:</h3>";
    $db->query("SHOW TABLES");
    $tables = $db->resultset();

    if (empty($tables)) {
        echo "<p style='color: red;'>❌ No tables found! Database may not be initialized.</p>";
    } else {
        echo "<ul>";
        foreach ($tables as $table) {
            $tableName = array_values((array)$table)[0];
            echo "<li>✅ " . $tableName . "</li>";
        }
        echo "</ul>";
    }

    // Test some basic queries
    echo "<h3>Sample Data:</h3>";

    // Count users
    $db->query("SELECT COUNT(*) as count FROM users");
    $userCount = $db->single();
    echo "<p>👥 Users: <strong>" . ($userCount->count ?? 0) . "</strong></p>";

    // Count events
    $db->query("SELECT COUNT(*) as count FROM events");
    $eventCount = $db->single();
    echo "<p>📅 Events: <strong>" . ($eventCount->count ?? 0) . "</strong></p>";

    // Count bookings
    $db->query("SELECT COUNT(*) as count FROM bookings");
    $bookingCount = $db->single();
    echo "<p>🎫 Bookings: <strong>" . ($bookingCount->count ?? 0) . "</strong></p>";

    echo "<h3>✅ All database tests passed!</h3>";
    echo "<p><a href='welcome.php'>Go to Welcome Page</a> | <a href='index.php'>Browse Events</a></p>";
    echo "<p><strong>New Port:</strong> Application now running on port 7823</p>";

} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Database Error:</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";

    echo "<h3>Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Make sure Docker containers are running: <code>docker ps</code></li>";
    echo "<li>Check MySQL container logs: <code>docker logs event_booking_mysql</code></li>";
    echo "<li>Verify database initialization files are loaded</li>";
    echo "</ul>";
}
?>
