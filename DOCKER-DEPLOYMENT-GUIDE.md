# 🐳 ZARA-Events Docker Deployment Guide

## 📦 Docker Hub Repository

**Image**: `zaramillion/zara-events:latest`  
**Docker Hub URL**: https://hub.docker.com/r/zaramillion/zara-events  
**Status**: ✅ Successfully pushed and available

## 🚀 Quick Deployment Options

### Option 1: Docker Run (Simple)

```bash
# Run the web application only
docker run -d \
  --name zara-events \
  -p 9000:80 \
  zaramillion/zara-events:latest
```

**Access**: http://localhost:9000

### Option 2: Docker Compose (Recommended)

Create a `docker-compose.yml` file:

```yaml
version: '3.8'

services:
  web:
    image: zaramillion/zara-events:latest
    container_name: zara_events_web
    restart: unless-stopped
    ports:
      - "9000:80"
    environment:
      - DB_HOST=mysql:3306
      - DB_USER=event_user
      - DB_PASS=event_password
      - DB_NAME=event_booking_system
      - ENVIRONMENT=production
    depends_on:
      - mysql
    networks:
      - event_booking_network

  mysql:
    image: mysql:8.0
    container_name: zara_events_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: event_booking_system
      MYSQL_USER: event_user
      MYSQL_PASSWORD: event_password
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - event_booking_network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: zara_events_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: event_user
      PMA_PASSWORD: event_password
    ports:
      - "8081:80"
    depends_on:
      - mysql
    networks:
      - event_booking_network

volumes:
  mysql_data:

networks:
  event_booking_network:
    driver: bridge
```

Then run:

```bash
docker-compose up -d
```

## 🌐 Production Deployment

### Cloud Platforms

#### 1. **DigitalOcean App Platform**
```bash
# Deploy directly from Docker Hub
doctl apps create --spec app-spec.yaml
```

#### 2. **AWS ECS/Fargate**
```bash
# Create task definition using zaramillion/zara-events:latest
aws ecs create-service --cluster zara-events --service-name zara-events-service
```

#### 3. **Google Cloud Run**
```bash
gcloud run deploy zara-events \
  --image zaramillion/zara-events:latest \
  --platform managed \
  --port 80 \
  --allow-unauthenticated
```

#### 4. **Azure Container Instances**
```bash
az container create \
  --resource-group zara-events-rg \
  --name zara-events \
  --image zaramillion/zara-events:latest \
  --ports 80
```

#### 5. **Heroku Container Registry**
```bash
heroku container:push web --app your-app-name
heroku container:release web --app your-app-name
```

## 🔧 Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `DB_HOST` | `mysql:3306` | Database host and port |
| `DB_USER` | `event_user` | Database username |
| `DB_PASS` | `event_password` | Database password |
| `DB_NAME` | `event_booking_system` | Database name |
| `ENVIRONMENT` | `production` | Application environment |
| `SMTP_HOST` | `smtp.gmail.com` | SMTP server host |
| `SMTP_PORT` | `587` | SMTP server port |
| `SMTP_USERNAME` | `<EMAIL>` | SMTP username |
| `SMTP_PASSWORD` | `pvjc rjit ogxg ncce` | SMTP password |

## 📊 Container Specifications

- **Base Image**: PHP 8.1 Apache
- **Size**: ~500MB
- **Architecture**: linux/amd64
- **Health Check**: Included
- **Restart Policy**: unless-stopped

## 🔍 Health Monitoring

The container includes a health check that verifies the web server is responding:

```bash
# Check container health
docker ps
# Look for "healthy" status

# Manual health check
curl -f http://localhost:9000/ || echo "Service unavailable"
```

## 📝 Logs and Debugging

```bash
# View container logs
docker logs zara-events

# Follow logs in real-time
docker logs -f zara-events

# Execute commands in running container
docker exec -it zara-events bash

# Check Apache error logs
docker exec zara-events tail -f /var/log/apache2/error.log
```

## 🔄 Updates and Maintenance

### Updating the Application

```bash
# Pull latest image
docker pull zaramillion/zara-events:latest

# Stop and remove old container
docker stop zara-events
docker rm zara-events

# Run new container
docker run -d --name zara-events -p 9000:80 zaramillion/zara-events:latest
```

### With Docker Compose

```bash
# Update and restart
docker-compose pull
docker-compose up -d
```

## 🛡️ Security Considerations

1. **Environment Variables**: Use Docker secrets or external secret management
2. **Database**: Use strong passwords and limit network access
3. **SSL/TLS**: Use reverse proxy (nginx) for HTTPS termination
4. **Firewall**: Restrict access to necessary ports only
5. **Updates**: Regularly update base images and dependencies

## 📈 Scaling Options

### Horizontal Scaling
```bash
# Scale web service to 3 replicas
docker-compose up -d --scale web=3
```

### Load Balancer Configuration
```nginx
upstream zara_events {
    server localhost:9001;
    server localhost:9002;
    server localhost:9003;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://zara_events;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🎯 Quick Start Commands

```bash
# 1. Pull the image
docker pull zaramillion/zara-events:latest

# 2. Run with database
docker-compose up -d

# 3. Access the application
open http://localhost:9000

# 4. Access phpMyAdmin
open http://localhost:8081
```

## 📞 Support

- **Developer**: Tayong Fritz
- **Email**: <EMAIL>
- **WhatsApp**: +237 651 408 682
- **Docker Hub**: https://hub.docker.com/r/zaramillion/zara-events

## 🏷️ Image Tags

- `latest` - Latest stable version
- `v1.0` - Initial release version

## 📋 Deployment Checklist

- [ ] Docker installed and running
- [ ] Image pulled: `zaramillion/zara-events:latest`
- [ ] Environment variables configured
- [ ] Database setup completed
- [ ] Ports available (9000, 3307, 8081)
- [ ] Health check passing
- [ ] Application accessible
- [ ] Email functionality tested

---

**🎉 Your ZARA-Events application is now containerized and ready for deployment anywhere Docker runs!**
