<?php
/**
 * Railway Database Connection Test Script
 * This script tests the connection to Railway MySQL database
 * and sets up the database schema if needed.
 */

echo "🚂 Railway Database Connection Test\n";
echo "====================================\n\n";

// Check if we're running inside Railway or externally
$isRailwayEnvironment = !empty(getenv('RAILWAY_ENVIRONMENT'));

if ($isRailwayEnvironment) {
    echo "✅ Running inside Railway environment\n";
    // Use internal Railway credentials
    putenv('MYSQLHOST=mysql.railway.internal');
    putenv('MYSQLPORT=3306');
    putenv('MYSQLUSER=root');
    putenv('MYSQLPASSWORD=DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU');
    putenv('MYSQLDATABASE=railway');
} else {
    echo "⚠️  Running outside Railway environment\n";
    echo "For external access, you need Railway TCP Proxy credentials.\n";
    echo "Please check your Railway dashboard for TCP Proxy settings.\n\n";

    // Check if external TCP proxy credentials are provided
    $externalHost = getenv('RAILWAY_TCP_PROXY_HOST');
    $externalPort = getenv('RAILWAY_TCP_PROXY_PORT');

    if ($externalHost && $externalPort) {
        echo "Using TCP Proxy credentials:\n";
        putenv("MYSQLHOST=$externalHost");
        putenv("MYSQLPORT=$externalPort");
        putenv('MYSQLUSER=root');
        putenv('MYSQLPASSWORD=DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU');
        putenv('MYSQLDATABASE=railway');
    } else {
        echo "❌ No TCP Proxy credentials found.\n";
        echo "To test externally, you need to:\n";
        echo "1. Go to your Railway MySQL service settings\n";
        echo "2. Enable TCP Proxy in Public Networking\n";
        echo "3. Set environment variables:\n";
        echo "   export RAILWAY_TCP_PROXY_HOST=your-proxy-host\n";
        echo "   export RAILWAY_TCP_PROXY_PORT=your-proxy-port\n\n";
        echo "For now, testing with internal credentials (will fail externally)...\n\n";

        // Use internal credentials anyway for demonstration
        putenv('MYSQLHOST=mysql.railway.internal');
        putenv('MYSQLPORT=3306');
        putenv('MYSQLUSER=root');
        putenv('MYSQLPASSWORD=DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU');
        putenv('MYSQLDATABASE=railway');
    }
}

// Include the updated config
require_once 'includes/config.php';

echo "=== Railway Database Connection Test ===\n\n";

// Display connection parameters (hide password)
echo "Connection Parameters:\n";
echo "Host: " . DB_HOST . "\n";
echo "Port: " . DB_PORT . "\n";
echo "User: " . DB_USER . "\n";
echo "Password: " . str_repeat('*', strlen(DB_PASS)) . "\n";
echo "Database: " . DB_NAME . "\n\n";

try {
    // Test database connection
    echo "Testing database connection...\n";

    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ":" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_PERSISTENT => false // Use false for testing
        ]
    );

    echo "✅ Database connection successful!\n\n";

    // Test basic query
    echo "Testing basic query...\n";
    $stmt = $pdo->query("SELECT VERSION() as version, NOW() as current_time");
    $result = $stmt->fetch();
    echo "✅ MySQL Version: " . $result['version'] . "\n";
    echo "✅ Current Time: " . $result['current_time'] . "\n\n";

    // Check if tables exist
    echo "Checking existing tables...\n";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

    if (empty($tables)) {
        echo "⚠️  No tables found. Database needs to be initialized.\n\n";

        // Ask if user wants to initialize
        echo "Do you want to initialize the database with the schema? (y/n): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);

        if (trim(strtolower($line)) === 'y') {
            echo "\nInitializing database...\n";

            // Read and execute the Railway setup script
            $sql = file_get_contents('database/railway-setup.sql');

            // Split by semicolon and execute each statement
            $statements = array_filter(array_map('trim', explode(';', $sql)));

            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^(--|\/\*)/', $statement)) {
                    try {
                        $pdo->exec($statement);
                    } catch (PDOException $e) {
                        // Ignore some common errors during setup
                        if (strpos($e->getMessage(), 'already exists') === false &&
                            strpos($e->getMessage(), 'Duplicate entry') === false) {
                            echo "⚠️  Warning: " . $e->getMessage() . "\n";
                        }
                    }
                }
            }

            echo "✅ Database initialized successfully!\n\n";
        }
    } else {
        echo "✅ Found " . count($tables) . " tables:\n";
        foreach ($tables as $table) {
            echo "  - $table\n";
        }
        echo "\n";
    }

    // Test the Database class from config.php
    echo "Testing Database class...\n";
    $db = new Database();
    $connection = $db->getConnection();

    if ($connection) {
        echo "✅ Database class working correctly!\n\n";

        // Test a sample query using the Database class
        echo "Testing sample query...\n";
        $db->query("SELECT COUNT(*) as user_count FROM users");
        $result = $db->single();
        echo "✅ Users in database: " . $result->user_count . "\n";

        $db->query("SELECT COUNT(*) as event_count FROM events");
        $result = $db->single();
        echo "✅ Events in database: " . $result->event_count . "\n\n";

        echo "🎉 Railway database is ready for use!\n";
        echo "\nTo use Railway database in your application:\n";
        echo "1. Set the following environment variables:\n";
        echo "   MYSQLHOST=mysql.railway.internal\n";
        echo "   MYSQLPORT=3306\n";
        echo "   MYSQLUSER=root\n";
        echo "   MYSQLPASSWORD=DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU\n";
        echo "   MYSQLDATABASE=railway\n\n";
        echo "2. Or update your deployment configuration to use these values.\n";

    } else {
        echo "❌ Database class connection failed!\n";
    }

} catch (PDOException $e) {
    echo "❌ Database connection failed!\n";
    echo "Error: " . $e->getMessage() . "\n\n";

    echo "Troubleshooting tips:\n";
    echo "1. Verify Railway database credentials\n";
    echo "2. Check if Railway database service is running\n";
    echo "3. Ensure your IP is whitelisted (if required)\n";
    echo "4. Verify network connectivity to Railway\n";
} catch (Exception $e) {
    echo "❌ Unexpected error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
?>
