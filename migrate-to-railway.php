<?php
/**
 * Data Migration Script from Local Docker MySQL to Railway
 * This script exports data from your local database and prepares it for Railway
 */

echo "=== ZARA-Events Database Migration to Railway ===\n\n";

// Local Docker database configuration
$local_config = [
    'host' => 'localhost',
    'port' => '3307', // Docker MySQL port
    'user' => 'event_user',
    'pass' => 'event_password',
    'name' => 'event_booking_system'
];

// Railway database configuration
$railway_config = [
    'host' => 'mysql.railway.internal',
    'port' => '3306',
    'user' => 'root',
    'pass' => 'DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU',
    'name' => 'railway'
];

function connectToDatabase($config, $name) {
    try {
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['user'], $config['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        echo "✅ Connected to $name database\n";
        return $pdo;
    } catch (PDOException $e) {
        echo "❌ Failed to connect to $name database: " . $e->getMessage() . "\n";
        return null;
    }
}

function exportTableData($pdo, $tableName) {
    try {
        $stmt = $pdo->query("SELECT * FROM $tableName");
        $data = $stmt->fetchAll();
        echo "✅ Exported " . count($data) . " records from $tableName\n";
        return $data;
    } catch (PDOException $e) {
        echo "⚠️  Warning: Could not export $tableName - " . $e->getMessage() . "\n";
        return [];
    }
}

function importTableData($pdo, $tableName, $data) {
    if (empty($data)) {
        echo "⚠️  No data to import for $tableName\n";
        return;
    }
    
    try {
        // Get column names from first row
        $columns = array_keys($data[0]);
        $placeholders = ':' . implode(', :', $columns);
        $columnList = implode(', ', $columns);
        
        $sql = "INSERT IGNORE INTO $tableName ($columnList) VALUES ($placeholders)";
        $stmt = $pdo->prepare($sql);
        
        $imported = 0;
        foreach ($data as $row) {
            try {
                $stmt->execute($row);
                if ($stmt->rowCount() > 0) {
                    $imported++;
                }
            } catch (PDOException $e) {
                // Skip duplicate entries
                if (strpos($e->getMessage(), 'Duplicate entry') === false) {
                    echo "⚠️  Warning importing row to $tableName: " . $e->getMessage() . "\n";
                }
            }
        }
        
        echo "✅ Imported $imported new records to $tableName\n";
    } catch (PDOException $e) {
        echo "❌ Failed to import data to $tableName: " . $e->getMessage() . "\n";
    }
}

// Step 1: Check local database connection
echo "Step 1: Connecting to local Docker database...\n";
$localPdo = connectToDatabase($local_config, 'local Docker');

if (!$localPdo) {
    echo "\n❌ Cannot connect to local database. Make sure Docker is running:\n";
    echo "   docker-compose up -d mysql\n\n";
    exit(1);
}

// Step 2: Connect to Railway database
echo "\nStep 2: Connecting to Railway database...\n";
$railwayPdo = connectToDatabase($railway_config, 'Railway');

if (!$railwayPdo) {
    echo "\n❌ Cannot connect to Railway database. Please check:\n";
    echo "1. Railway MySQL service is running\n";
    echo "2. Database credentials are correct\n";
    echo "3. Network connectivity to Railway\n\n";
    exit(1);
}

// Step 3: Initialize Railway database schema
echo "\nStep 3: Setting up Railway database schema...\n";
try {
    $sql = file_get_contents('database/railway-setup.sql');
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^(--|\/\*)/', $statement)) {
            try {
                $railwayPdo->exec($statement);
            } catch (PDOException $e) {
                // Ignore table exists errors
                if (strpos($e->getMessage(), 'already exists') === false) {
                    echo "⚠️  Warning: " . $e->getMessage() . "\n";
                }
            }
        }
    }
    echo "✅ Railway database schema ready\n";
} catch (Exception $e) {
    echo "❌ Failed to setup Railway schema: " . $e->getMessage() . "\n";
    exit(1);
}

// Step 4: Export and import data
echo "\nStep 4: Migrating data...\n";

$tables = ['users', 'events', 'bookings', 'cart', 'user_sessions', 'password_reset_tokens', 'payments'];

foreach ($tables as $table) {
    echo "\nMigrating $table...\n";
    $data = exportTableData($localPdo, $table);
    importTableData($railwayPdo, $table, $data);
}

// Step 5: Verify migration
echo "\nStep 5: Verifying migration...\n";
foreach ($tables as $table) {
    try {
        $localStmt = $localPdo->query("SELECT COUNT(*) as count FROM $table");
        $localCount = $localStmt->fetch()['count'];
        
        $railwayStmt = $railwayPdo->query("SELECT COUNT(*) as count FROM $table");
        $railwayCount = $railwayStmt->fetch()['count'];
        
        if ($localCount == $railwayCount) {
            echo "✅ $table: $railwayCount records (matches local)\n";
        } else {
            echo "⚠️  $table: Railway=$railwayCount, Local=$localCount (difference may be due to duplicates)\n";
        }
    } catch (PDOException $e) {
        echo "⚠️  Could not verify $table: " . $e->getMessage() . "\n";
    }
}

echo "\n🎉 Migration completed!\n\n";
echo "Next steps:\n";
echo "1. Test the Railway connection: php test-railway-connection.php\n";
echo "2. Update your application environment variables with Railway credentials\n";
echo "3. Deploy your application to use the Railway database\n";
echo "\nRailway Database Credentials:\n";
echo "MYSQLHOST=mysql.railway.internal\n";
echo "MYSQLPORT=3306\n";
echo "MYSQLUSER=root\n";
echo "MYSQLPASSWORD=DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU\n";
echo "MYSQLDATABASE=railway\n";

?>
