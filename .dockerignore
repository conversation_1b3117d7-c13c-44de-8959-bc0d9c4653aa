# Git
.git
.gitignore

# Docker (keep docker-compose.yml for reference)
.dockerignore

# Documentation
README.md
*.md
DOCKER-README.md
SETUP-COMPLETE.md
API-DOCUMENTATION.md
UI-DOCUMENTATION.md
UI-ENHANCEMENT-REPORT.md
email-setup-guide.md
IMPLEMENTATION-SUMMARY.md

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp

# Development files
test-email.php
test-email-cli.php
setup.php

# Environment files (will be set via Docker)
.env

# Node modules (if any)
node_modules/

# Vendor (will be installed via Composer in Docker)
vendor/

# Cache
cache/
tmp/
