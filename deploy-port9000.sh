#!/bin/bash

# ZARA-Events Deployment Script - Port 9000
# This script ensures the application runs on port 9000

echo "🚀 ZARA-Events Deployment Script"
echo "================================="
echo "Target Port: 9000"
echo "Docker Image: zaramillion/zara-events:latest"
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Stop and remove existing container if it exists
echo "🧹 Cleaning up existing containers..."
docker stop zara-events-app 2>/dev/null || true
docker rm zara-events-app 2>/dev/null || true

# Pull the latest image
echo "📥 Pulling latest image from Docker Hub..."
docker pull zaramillion/zara-events:latest

if [ $? -ne 0 ]; then
    echo "❌ Failed to pull image from Docker Hub"
    exit 1
fi

# Check if port 9000 is available
if lsof -Pi :9000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  Port 9000 is already in use. Attempting to free it..."
    # Try to stop any existing container using port 9000
    CONTAINER_ID=$(docker ps --filter "publish=9000" --format "{{.ID}}")
    if [ ! -z "$CONTAINER_ID" ]; then
        echo "🛑 Stopping container using port 9000: $CONTAINER_ID"
        docker stop $CONTAINER_ID
        docker rm $CONTAINER_ID
    fi
fi

# Run the container on port 9000
echo "🚀 Starting ZARA-Events on port 9000..."
docker run -d \
    --name zara-events-app \
    --restart unless-stopped \
    -p 9000:80 \
    -e ENVIRONMENT=production \
    zaramillion/zara-events:latest

if [ $? -eq 0 ]; then
    echo "✅ ZARA-Events deployed successfully!"
    echo ""
    echo "🌐 Application URLs:"
    echo "   Main App: http://localhost:9000"
    echo "   About:    http://localhost:9000/about.php"
    echo "   Contact:  http://localhost:9000/contact.php"
    echo "   Help:     http://localhost:9000/help-center.php"
    echo ""
    echo "📊 Container Status:"
    docker ps --filter "name=zara-events-app" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo ""
    echo "📝 To view logs: docker logs -f zara-events-app"
    echo "🛑 To stop: docker stop zara-events-app"
    echo ""
    
    # Wait a moment for the container to fully start
    sleep 3
    
    # Test if the application is responding
    echo "🔍 Testing application health..."
    if curl -f http://localhost:9000 > /dev/null 2>&1; then
        echo "✅ Application is responding on port 9000"
        echo "🎉 Deployment completed successfully!"
    else
        echo "⚠️  Application may still be starting up. Please check in a moment."
    fi
else
    echo "❌ Failed to deploy ZARA-Events"
    exit 1
fi
