# 🚀 ZARA-Events: Deployment & DevOps Guide

## 🎯 Deployment Overview

### Deployment Options
1. **Local Development**: Docker Compose
2. **Cloud Deployment**: Google Cloud Run
3. **VPS/Dedicated**: Traditional server setup
4. **Container Platforms**: Kubernetes, AWS ECS, Azure Container Instances

### Current Status
- ✅ **Docker Image**: `zaramillion/zara-events:latest`
- ✅ **Docker Hub**: Successfully pushed and available
- ✅ **Local Testing**: Running on port 9000
- ✅ **Cloud Ready**: Configured for Google Cloud Run

## 🐳 Docker Configuration

### Dockerfile Analysis
```dockerfile
FROM php:8.1-apache

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git curl libpng-dev libonig-dev libxml2-dev \
    zip unzip libzip-dev && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install pdo pdo_mysql mysqli mbstring exif pcntl bcmath gd zip

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Enable Apache mod_rewrite
RUN a2enmod rewrite

# Configure Apache
RUN echo '<Directory /var/www/html>\n\
    Options Indexes FollowSymLinks\n\
    AllowOverride All\n\
    Require all granted\n\
</Directory>' > /etc/apache2/conf-available/zara-events.conf \
    && a2enconf zara-events

# Set working directory
WORKDIR /var/www/html

# Copy and install dependencies
COPY composer.json composer.lock ./
RUN composer install --no-dev --optimize-autoloader --no-interaction

# Copy application files
COPY . /var/www/html/

# Create directories and set permissions
RUN mkdir -p /var/www/html/assets/images/events /var/www/html/logs && \
    chown -R www-data:www-data /var/www/html && \
    chmod -R 755 /var/www/html && \
    chmod -R 777 /var/www/html/assets/images && \
    chmod -R 777 /var/www/html/logs

# Environment variables
ENV APACHE_DOCUMENT_ROOT /var/www/html
ENV ENVIRONMENT production

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

EXPOSE 80
CMD ["apache2-foreground"]
```

### Docker Compose Configuration
```yaml
version: '3.8'

services:
  # ZARA-Events Web Application
  web:
    image: zaramillion/zara-events:latest
    container_name: zara_events_web
    restart: unless-stopped
    ports:
      - "9000:80"
    environment:
      - DB_HOST=mysql:3306
      - DB_USER=event_user
      - DB_PASS=event_password
      - DB_NAME=event_booking_system
      - ENVIRONMENT=production
    depends_on:
      - mysql
    networks:
      - event_booking_network
    volumes:
      - ./assets/images:/var/www/html/assets/images
      - ./logs:/var/www/html/logs

  # MySQL Database Service
  mysql:
    image: mysql:8.0
    container_name: event_booking_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: event_booking_system
      MYSQL_USER: event_user
      MYSQL_PASSWORD: event_password
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./database/init-data.sql:/docker-entrypoint-initdb.d/02-init-data.sql
    networks:
      - event_booking_network
    command: --default-authentication-plugin=mysql_native_password

  # phpMyAdmin for database management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: event_booking_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: event_user
      PMA_PASSWORD: event_password
    ports:
      - "8081:80"
    depends_on:
      - mysql
    networks:
      - event_booking_network

volumes:
  mysql_data:
    driver: local

networks:
  event_booking_network:
    driver: bridge
```

## ☁️ Google Cloud Run Deployment

### Prerequisites
```bash
# Install Google Cloud CLI
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# Authenticate
gcloud auth login
gcloud config set project YOUR_PROJECT_ID

# Enable APIs
gcloud services enable run.googleapis.com
gcloud services enable sql-component.googleapis.com
```

### Deployment Commands

#### Basic Deployment
```bash
gcloud run deploy zara-events \
  --image=zaramillion/zara-events:latest \
  --platform=managed \
  --region=us-central1 \
  --allow-unauthenticated \
  --port=80 \
  --memory=1Gi \
  --cpu=1
```

#### Advanced Deployment with Environment Variables
```bash
gcloud run deploy zara-events \
  --image=zaramillion/zara-events:latest \
  --platform=managed \
  --region=us-central1 \
  --allow-unauthenticated \
  --port=80 \
  --memory=1Gi \
  --cpu=1 \
  --max-instances=10 \
  --set-env-vars="ENVIRONMENT=production,SMTP_HOST=smtp.gmail.com,SMTP_PORT=587,SMTP_USERNAME=<EMAIL>,SMTP_PASSWORD=pvjc rjit ogxg ncce"
```

### Cloud SQL Setup
```bash
# Create Cloud SQL instance
gcloud sql instances create zara-events-db \
  --database-version=MYSQL_8_0 \
  --tier=db-f1-micro \
  --region=us-central1 \
  --root-password=secure_root_password

# Create database
gcloud sql databases create event_booking_system \
  --instance=zara-events-db

# Create user
gcloud sql users create event_user \
  --instance=zara-events-db \
  --password=secure_user_password

# Get connection name
gcloud sql instances describe zara-events-db --format="value(connectionName)"
```

## 🔧 Environment Configuration

### Production Environment Variables
```bash
# Database Configuration
DB_HOST=your-cloud-sql-ip:3306
DB_USER=event_user
DB_PASS=secure_password
DB_NAME=event_booking_system

# Application Settings
ENVIRONMENT=production
SITE_URL=https://your-domain.com
ADMIN_EMAIL=<EMAIL>

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=ZARA-Events

# Security Settings
SESSION_LIFETIME=3600
MAX_LOGIN_ATTEMPTS=5
PASSWORD_MIN_LENGTH=8
```

### Development Environment Variables
```bash
# Database Configuration
DB_HOST=mysql:3306
DB_USER=event_user
DB_PASS=event_password
DB_NAME=event_booking_system

# Application Settings
ENVIRONMENT=development
SITE_URL=http://localhost:9000
DEBUG=true

# Email Configuration (same as production)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=pvjc rjit ogxg ncce
```

## 📊 Monitoring & Logging

### Application Logging
```php
<?php
// Custom logging function
function logActivity($message, $level = 'INFO', $context = []) {
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? json_encode($context) : '';
    $logMessage = "[$timestamp] [$level] $message $contextStr\n";
    
    // Log to file
    file_put_contents('/var/www/html/logs/app.log', $logMessage, FILE_APPEND | LOCK_EX);
    
    // Log to system log in production
    if (getenv('ENVIRONMENT') === 'production') {
        error_log($logMessage);
    }
}

// Usage examples
logActivity('User login successful', 'INFO', ['user_id' => $userId]);
logActivity('Database connection failed', 'ERROR', ['error' => $e->getMessage()]);
```

### Health Check Endpoint
```php
<?php
// health-check.php
header('Content-Type: application/json');

$health = [
    'status' => 'healthy',
    'timestamp' => date('c'),
    'version' => '1.0.0',
    'checks' => []
];

// Database check
try {
    $pdo = getDBConnection();
    $stmt = $pdo->query('SELECT 1');
    $health['checks']['database'] = 'healthy';
} catch (Exception $e) {
    $health['checks']['database'] = 'unhealthy';
    $health['status'] = 'unhealthy';
}

// Email check
try {
    $emailManager = new EmailManager();
    $health['checks']['email'] = 'healthy';
} catch (Exception $e) {
    $health['checks']['email'] = 'unhealthy';
}

// Disk space check
$freeBytes = disk_free_space('/var/www/html');
$totalBytes = disk_total_space('/var/www/html');
$usagePercent = (($totalBytes - $freeBytes) / $totalBytes) * 100;

$health['checks']['disk_usage'] = $usagePercent < 90 ? 'healthy' : 'warning';

http_response_code($health['status'] === 'healthy' ? 200 : 503);
echo json_encode($health, JSON_PRETTY_PRINT);
?>
```

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow
```yaml
name: Deploy to Cloud Run

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  PROJECT_ID: your-project-id
  SERVICE: zara-events
  REGION: us-central1

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - name: Setup Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      with:
        project_id: ${{ env.PROJECT_ID }}
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        export_default_credentials: true

    - name: Configure Docker
      run: gcloud auth configure-docker

    - name: Build Docker image
      run: |
        docker build -t gcr.io/$PROJECT_ID/$SERVICE:$GITHUB_SHA .
        docker tag gcr.io/$PROJECT_ID/$SERVICE:$GITHUB_SHA gcr.io/$PROJECT_ID/$SERVICE:latest

    - name: Push Docker image
      run: |
        docker push gcr.io/$PROJECT_ID/$SERVICE:$GITHUB_SHA
        docker push gcr.io/$PROJECT_ID/$SERVICE:latest

    - name: Deploy to Cloud Run
      run: |
        gcloud run deploy $SERVICE \
          --image gcr.io/$PROJECT_ID/$SERVICE:$GITHUB_SHA \
          --platform managed \
          --region $REGION \
          --allow-unauthenticated \
          --port 80 \
          --memory 1Gi
```

## 🛡️ Security Configuration

### SSL/HTTPS Setup
```nginx
# Nginx configuration for SSL termination
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    location / {
        proxy_pass http://localhost:9000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

### Firewall Configuration
```bash
# UFW firewall rules
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 3306/tcp  # Deny direct database access
sudo ufw enable
```

## 📈 Performance Optimization

### Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_events_date ON events(event_date);
CREATE INDEX idx_events_category ON events(category);
CREATE INDEX idx_bookings_user ON bookings(user_id);
CREATE INDEX idx_bookings_event ON bookings(event_id);
CREATE INDEX idx_users_email ON users(email);
```

### Caching Strategy
```php
<?php
// Simple file-based caching
class SimpleCache {
    private $cacheDir = '/tmp/cache/';
    
    public function get($key) {
        $file = $this->cacheDir . md5($key) . '.cache';
        if (file_exists($file) && (time() - filemtime($file)) < 3600) {
            return unserialize(file_get_contents($file));
        }
        return null;
    }
    
    public function set($key, $data) {
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        $file = $this->cacheDir . md5($key) . '.cache';
        file_put_contents($file, serialize($data));
    }
}
```

This deployment guide ensures:
- ✅ **Scalable Infrastructure**: Cloud-ready architecture
- ✅ **Security**: SSL, firewall, and security best practices
- ✅ **Monitoring**: Health checks and logging
- ✅ **Performance**: Optimization strategies
- ✅ **Automation**: CI/CD pipeline ready
