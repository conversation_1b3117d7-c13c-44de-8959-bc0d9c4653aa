# 🔧 ZARA-Events: Implementation & Code Structure

## 📁 Project Structure

### Directory Organization
```
zara-events/
├── 📁 admin/                    # Admin panel modules
│   ├── index.php               # Admin dashboard
│   ├── events/                 # Event management
│   ├── users/                  # User management
│   └── bookings/               # Booking management
├── 📁 assets/                   # Static resources
│   ├── css/                    # Stylesheets
│   │   ├── modern-ui.css       # Main stylesheet
│   │   └── admin.css           # Admin styles
│   ├── js/                     # JavaScript files
│   │   ├── modern-app.js       # Main application JS
│   │   └── admin.js            # Admin functionality
│   └── images/                 # Image assets
├── 📁 auth/                     # Authentication system
│   ├── login.php               # User login
│   ├── register.php            # User registration
│   ├── logout.php              # User logout
│   └── reset-password.php      # Password reset
├── 📁 booking/                  # Booking system
│   ├── cart.php                # Shopping cart
│   ├── checkout.php            # Checkout process
│   ├── confirmation.php        # Booking confirmation
│   └── ticket.php              # Ticket generation
├── 📁 database/                 # Database files
│   ├── schema.sql              # Database schema
│   ├── init-data.sql           # Sample data
│   └── migrations/             # Database migrations
├── 📁 events/                   # Event management
│   ├── index.php               # Event listing
│   ├── details.php             # Event details
│   ├── search.php              # Event search
│   └── category.php            # Category filtering
├── 📁 includes/                 # Shared components
│   ├── config.php              # Configuration
│   ├── functions.php           # Utility functions
│   ├── email.php               # Email functionality
│   ├── header.php              # Page header
│   └── footer.php              # Page footer
├── 📁 user/                     # User dashboard
│   ├── dashboard.php           # User dashboard
│   ├── profile.php             # Profile management
│   ├── bookings.php            # User bookings
│   └── settings.php            # User settings
├── 📁 support/                  # Support pages
│   ├── about.php               # About page
│   ├── contact.php             # Contact form
│   ├── help-center.php         # Help center
│   ├── privacy-policy.php      # Privacy policy
│   └── terms-of-service.php    # Terms of service
├── 📁 docs/                     # Documentation
├── docker-compose.yml          # Docker configuration
├── Dockerfile                  # Docker image definition
└── index.php                   # Homepage/Welcome page
```

## 🏗️ Core Implementation

### 1. Configuration Management (`includes/config.php`)

```php
<?php
// Database configuration
define('DB_HOST', getenv('DB_HOST') ?: 'mysql:3306');
define('DB_USER', getenv('DB_USER') ?: 'event_user');
define('DB_PASS', getenv('DB_PASS') ?: 'event_password');
define('DB_NAME', getenv('DB_NAME') ?: 'event_booking_system');

// Application settings
define('SITE_NAME', 'ZARA-Events');
define('SITE_URL', getenv('SITE_URL') ?: 'http://localhost:9000');
define('ADMIN_EMAIL', getenv('ADMIN_EMAIL') ?: '<EMAIL>');

// Email configuration
define('SMTP_HOST', getenv('SMTP_HOST') ?: 'smtp.gmail.com');
define('SMTP_PORT', getenv('SMTP_PORT') ?: 587);
define('SMTP_USERNAME', getenv('SMTP_USERNAME') ?: '<EMAIL>');
define('SMTP_PASSWORD', getenv('SMTP_PASSWORD') ?: 'pvjc rjit ogxg ncce');

// Security settings
define('SESSION_LIFETIME', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('PASSWORD_MIN_LENGTH', 8);
```

### 2. Database Connection (`includes/functions.php`)

```php
<?php
function getDBConnection() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            die("Database connection failed");
        }
    }
    
    return $pdo;
}
```

### 3. Security Functions

```php
<?php
// Input sanitization
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

// Password hashing
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// Password verification
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// CSRF token generation
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// CSRF token validation
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}
```

### 4. Authentication System

```php
<?php
// User login
function loginUser($email, $password) {
    $pdo = getDBConnection();
    
    $stmt = $pdo->prepare("SELECT id, email, password, first_name, last_name, role FROM users WHERE email = ? AND status = 'active'");
    $stmt->execute([$email]);
    $user = $stmt->fetch();
    
    if ($user && verifyPassword($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['first_name'] = $user['first_name'];
        $_SESSION['last_name'] = $user['last_name'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['login_time'] = time();
        
        // Update last login
        $updateStmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $updateStmt->execute([$user['id']]);
        
        return true;
    }
    
    return false;
}

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']) && 
           isset($_SESSION['login_time']) && 
           (time() - $_SESSION['login_time']) < SESSION_LIFETIME;
}

// Check if user is admin
function isAdmin() {
    return isLoggedIn() && $_SESSION['role'] === 'admin';
}
```

## 📧 Email System Implementation

### Email Manager Class (`includes/email.php`)

```php
<?php
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class EmailManager {
    public $mailer;
    
    public function __construct() {
        $this->mailer = new PHPMailer(true);
        $this->configureMailer();
    }
    
    private function configureMailer() {
        $this->mailer->isSMTP();
        $this->mailer->Host = SMTP_HOST;
        $this->mailer->SMTPAuth = true;
        $this->mailer->Username = SMTP_USERNAME;
        $this->mailer->Password = SMTP_PASSWORD;
        $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $this->mailer->Port = SMTP_PORT;
        $this->mailer->setFrom(SMTP_USERNAME, 'ZARA-Events');
    }
    
    public function sendBookingConfirmation($userEmail, $userName, $bookingDetails) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($userEmail, $userName);
            
            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Booking Confirmation - ZARA-Events';
            $this->mailer->Body = $this->generateBookingEmailTemplate($bookingDetails);
            
            return $this->mailer->send();
        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }
    
    private function generateBookingEmailTemplate($details) {
        return "
        <html>
        <body style='font-family: Arial, sans-serif;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h2 style='color: #17A2B8;'>Booking Confirmation</h2>
                <p>Dear {$details['user_name']},</p>
                <p>Your booking has been confirmed!</p>
                
                <div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>
                    <h3>Event Details:</h3>
                    <p><strong>Event:</strong> {$details['event_title']}</p>
                    <p><strong>Date:</strong> {$details['event_date']}</p>
                    <p><strong>Tickets:</strong> {$details['quantity']}</p>
                    <p><strong>Total:</strong> {$details['total_amount']} XAF</p>
                </div>
                
                <p>Thank you for choosing ZARA-Events!</p>
                <p>Best regards,<br>ZARA-Events Team</p>
            </div>
        </body>
        </html>";
    }
}
```

## 🛒 Booking System Implementation

### Shopping Cart Management

```php
<?php
class CartManager {
    
    public static function addToCart($eventId, $quantity = 1) {
        if (!isset($_SESSION['cart'])) {
            $_SESSION['cart'] = [];
        }
        
        if (isset($_SESSION['cart'][$eventId])) {
            $_SESSION['cart'][$eventId] += $quantity;
        } else {
            $_SESSION['cart'][$eventId] = $quantity;
        }
        
        return true;
    }
    
    public static function removeFromCart($eventId) {
        if (isset($_SESSION['cart'][$eventId])) {
            unset($_SESSION['cart'][$eventId]);
            return true;
        }
        return false;
    }
    
    public static function getCartItems() {
        if (!isset($_SESSION['cart']) || empty($_SESSION['cart'])) {
            return [];
        }
        
        $pdo = getDBConnection();
        $eventIds = array_keys($_SESSION['cart']);
        $placeholders = str_repeat('?,', count($eventIds) - 1) . '?';
        
        $stmt = $pdo->prepare("SELECT * FROM events WHERE id IN ($placeholders)");
        $stmt->execute($eventIds);
        $events = $stmt->fetchAll();
        
        $cartItems = [];
        foreach ($events as $event) {
            $event['quantity'] = $_SESSION['cart'][$event['id']];
            $event['subtotal'] = $event['price'] * $event['quantity'];
            $cartItems[] = $event;
        }
        
        return $cartItems;
    }
    
    public static function getCartTotal() {
        $items = self::getCartItems();
        $total = 0;
        
        foreach ($items as $item) {
            $total += $item['subtotal'];
        }
        
        return $total;
    }
    
    public static function clearCart() {
        unset($_SESSION['cart']);
    }
}
```

## 🎨 Frontend Implementation

### Modern UI Styling (`assets/css/modern-ui.css`)

```css
:root {
    --primary-color: #17A2B8;
    --secondary-color: #6C757D;
    --success-color: #28A745;
    --danger-color: #DC3545;
    --warning-color: #FFC107;
    --info-color: #17A2B8;
    --light-bg: #F8F9FA;
    --dark-bg: #343A40;
    --soft-ivory: #F8F6F0;
    --tropical-teal: #17A2B8;
}

/* Modern Card Design */
.card-modern {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-modern:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

/* Gradient Backgrounds */
.gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.gradient-success {
    background: linear-gradient(135deg, var(--success-color), #20C997);
}

/* Modern Buttons */
.btn-primary-modern {
    background: linear-gradient(135deg, var(--primary-color), #138496);
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(23, 162, 184, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-modern {
        margin-bottom: 20px;
    }
    
    .btn-primary-modern {
        width: 100%;
        margin-bottom: 10px;
    }
}
```

### JavaScript Functionality (`assets/js/modern-app.js`)

```javascript
// Modern App JavaScript
class ZaraEventsApp {
    constructor() {
        this.initializeApp();
    }
    
    initializeApp() {
        this.setupEventListeners();
        this.initializeComponents();
        this.loadCartCount();
    }
    
    setupEventListeners() {
        // Add to cart functionality
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('add-to-cart')) {
                this.addToCart(e.target);
            }
        });
        
        // Search functionality
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', this.handleSearch.bind(this));
        }
    }
    
    addToCart(button) {
        const eventId = button.dataset.eventId;
        const quantity = button.dataset.quantity || 1;
        
        fetch('/api/cart/add.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                event_id: eventId,
                quantity: quantity
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification('Item added to cart!', 'success');
                this.updateCartCount();
            } else {
                this.showNotification('Failed to add item to cart', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.showNotification('An error occurred', 'error');
        });
    }
    
    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} notification`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
    
    updateCartCount() {
        fetch('/api/cart/count.php')
        .then(response => response.json())
        .then(data => {
            const cartBadge = document.querySelector('.cart-count');
            if (cartBadge) {
                cartBadge.textContent = data.count;
                cartBadge.style.display = data.count > 0 ? 'inline' : 'none';
            }
        });
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ZaraEventsApp();
});
```

This modular implementation approach ensures:
- ✅ **Maintainable Code**: Each component is focused and manageable
- ✅ **Scalable Architecture**: Easy to extend and modify
- ✅ **Security**: Built-in security measures throughout
- ✅ **Performance**: Optimized for speed and efficiency
- ✅ **Responsive**: Mobile-first design approach
