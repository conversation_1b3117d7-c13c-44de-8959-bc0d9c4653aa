# 🔧 ZARA-Events: API Documentation & Troubleshooting

## 📡 API Documentation

### API Overview
ZARA-Events provides internal APIs for frontend-backend communication and potential third-party integrations.

**Base URL**: `http://localhost:9000/api/` (development)  
**Production URL**: `https://your-domain.com/api/`

### Authentication
Most API endpoints require user authentication via session cookies.

```php
// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit;
}
```

### Response Format
All API responses follow a consistent JSON format:

```json
{
    "success": true,
    "data": {},
    "message": "Operation successful",
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### Error Response Format
```json
{
    "success": false,
    "error": "Error description",
    "code": "ERROR_CODE",
    "timestamp": "2024-01-01T12:00:00Z"
}
```

## 🛒 Cart API Endpoints

### Add to Cart
**Endpoint**: `POST /api/cart/add.php`

**Request Body**:
```json
{
    "event_id": 123,
    "quantity": 2
}
```

**Response**:
```json
{
    "success": true,
    "message": "Item added to cart",
    "cart_count": 3
}
```

**Implementation**:
```php
<?php
// api/cart/add.php
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$eventId = (int)$input['event_id'];
$quantity = (int)($input['quantity'] ?? 1);

if ($eventId <= 0 || $quantity <= 0) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid parameters']);
    exit;
}

// Add to cart logic
CartManager::addToCart($eventId, $quantity);

echo json_encode([
    'success' => true,
    'message' => 'Item added to cart',
    'cart_count' => count($_SESSION['cart'] ?? [])
]);
?>
```

### Get Cart Count
**Endpoint**: `GET /api/cart/count.php`

**Response**:
```json
{
    "success": true,
    "count": 3
}
```

### Remove from Cart
**Endpoint**: `DELETE /api/cart/remove.php`

**Request Body**:
```json
{
    "event_id": 123
}
```

## 🎪 Events API Endpoints

### Get Events
**Endpoint**: `GET /api/events/list.php`

**Query Parameters**:
- `category`: Filter by category
- `search`: Search term
- `limit`: Number of results (default: 10)
- `offset`: Pagination offset

**Response**:
```json
{
    "success": true,
    "data": {
        "events": [
            {
                "id": 1,
                "title": "Music Festival 2024",
                "description": "Amazing music festival",
                "date": "2024-06-15",
                "price": 25000,
                "category": "Music",
                "image": "festival.jpg"
            }
        ],
        "total": 50,
        "limit": 10,
        "offset": 0
    }
}
```

### Get Event Details
**Endpoint**: `GET /api/events/details.php?id=123`

**Response**:
```json
{
    "success": true,
    "data": {
        "id": 123,
        "title": "Music Festival 2024",
        "description": "Full description...",
        "date": "2024-06-15",
        "time": "18:00:00",
        "location": "Yaoundé Stadium",
        "price": 25000,
        "capacity": 5000,
        "available_tickets": 4500,
        "category": "Music",
        "organizer": "Event Organizer Name",
        "images": ["image1.jpg", "image2.jpg"]
    }
}
```

## 👤 User API Endpoints

### Get User Profile
**Endpoint**: `GET /api/user/profile.php`

**Response**:
```json
{
    "success": true,
    "data": {
        "id": 123,
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "phone": "+237123456789",
        "created_at": "2024-01-01T12:00:00Z"
    }
}
```

### Update Profile
**Endpoint**: `PUT /api/user/profile.php`

**Request Body**:
```json
{
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+237123456789"
}
```

### Get User Bookings
**Endpoint**: `GET /api/user/bookings.php`

**Response**:
```json
{
    "success": true,
    "data": {
        "bookings": [
            {
                "id": 456,
                "event_title": "Music Festival 2024",
                "event_date": "2024-06-15",
                "quantity": 2,
                "total_amount": 50000,
                "status": "confirmed",
                "booking_date": "2024-01-01T12:00:00Z"
            }
        ]
    }
}
```

## 🛠️ Troubleshooting Guide

### Common Issues & Solutions

#### 1. Database Connection Failed
**Error**: `Database connection failed: SQLSTATE[HY000] [2002]`

**Causes & Solutions**:
```bash
# Check if MySQL container is running
docker ps | grep mysql

# Restart MySQL container
docker-compose restart mysql

# Check database credentials in config.php
# Verify DB_HOST, DB_USER, DB_PASS, DB_NAME

# Test connection manually
docker exec zara_events_web php -r "
try {
    \$pdo = new PDO('mysql:host=mysql:3306;dbname=event_booking_system', 'event_user', 'event_password');
    echo 'Connection successful';
} catch (Exception \$e) {
    echo 'Connection failed: ' . \$e->getMessage();
}
"
```

#### 2. Email Not Sending
**Error**: `SMTP Error: Could not authenticate`

**Solutions**:
```php
// Check email configuration
var_dump([
    'SMTP_HOST' => SMTP_HOST,
    'SMTP_PORT' => SMTP_PORT,
    'SMTP_USERNAME' => SMTP_USERNAME,
    'SMTP_PASSWORD' => substr(SMTP_PASSWORD, 0, 4) . '****'
]);

// Test email manually
$emailManager = new EmailManager();
$result = $emailManager->sendTestEmail();
var_dump($result);

// Common fixes:
// 1. Verify Gmail app password
// 2. Enable 2-factor authentication
// 3. Check firewall blocking port 587
// 4. Verify SMTP credentials
```

#### 3. Session Issues
**Error**: `User not logged in` or session expires quickly

**Solutions**:
```php
// Check session configuration
ini_get('session.gc_maxlifetime');
ini_get('session.cookie_lifetime');

// Debug session data
session_start();
var_dump($_SESSION);

// Common fixes:
// 1. Check session.save_path permissions
// 2. Verify session cookies are enabled
// 3. Check for session conflicts
// 4. Clear browser cookies
```

#### 4. File Upload Issues
**Error**: `Failed to upload image`

**Solutions**:
```bash
# Check directory permissions
ls -la assets/images/
chmod 777 assets/images/

# Check PHP upload settings
php -i | grep upload

# Common fixes:
# 1. Increase upload_max_filesize
# 2. Increase post_max_size
# 3. Check file type restrictions
# 4. Verify directory exists and writable
```

#### 5. Docker Container Issues
**Error**: Container fails to start or crashes

**Solutions**:
```bash
# Check container logs
docker logs zara_events_web

# Check container status
docker ps -a

# Restart containers
docker-compose down
docker-compose up -d

# Rebuild if needed
docker-compose build --no-cache
docker-compose up -d

# Common fixes:
# 1. Check port conflicts
# 2. Verify Docker daemon running
# 3. Check available disk space
# 4. Update Docker images
```

### Performance Issues

#### Slow Page Loading
**Diagnosis**:
```bash
# Check server resources
docker stats

# Check database performance
docker exec event_booking_mysql mysql -u event_user -pevent_password -e "SHOW PROCESSLIST;"

# Check Apache logs
docker logs zara_events_web | tail -50
```

**Solutions**:
1. **Database Optimization**:
   ```sql
   -- Add missing indexes
   SHOW INDEX FROM events;
   CREATE INDEX idx_events_date ON events(event_date);
   ```

2. **Image Optimization**:
   ```bash
   # Compress images
   find assets/images -name "*.jpg" -exec jpegoptim --max=80 {} \;
   ```

3. **Caching**:
   ```php
   // Implement simple caching
   $cacheFile = 'cache/events_' . md5($query) . '.json';
   if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < 300) {
       return json_decode(file_get_contents($cacheFile), true);
   }
   ```

### Security Issues

#### Suspicious Activity
**Monitoring**:
```php
// Log suspicious activities
function logSuspiciousActivity($activity, $ip, $userAgent) {
    $logEntry = [
        'timestamp' => date('c'),
        'activity' => $activity,
        'ip' => $ip,
        'user_agent' => $userAgent,
        'session_id' => session_id()
    ];
    
    file_put_contents('logs/security.log', json_encode($logEntry) . "\n", FILE_APPEND);
}

// Usage
if ($failedLoginAttempts > 5) {
    logSuspiciousActivity('Multiple failed logins', $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']);
}
```

#### SQL Injection Attempts
**Prevention & Detection**:
```php
// Always use prepared statements
$stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = ?");
$stmt->execute([$email, 'active']);

// Log potential SQL injection attempts
if (preg_match('/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bDELETE\b|\bDROP\b)/i', $input)) {
    logSuspiciousActivity('Potential SQL injection', $_SERVER['REMOTE_ADDR'], $input);
}
```

### Deployment Issues

#### Cloud Run Deployment Fails
**Common Solutions**:
```bash
# Check image exists
docker pull zaramillion/zara-events:latest

# Verify Cloud Run service configuration
gcloud run services describe zara-events --region=us-central1

# Check logs
gcloud run services logs read zara-events --region=us-central1

# Redeploy with verbose output
gcloud run deploy zara-events \
  --image=zaramillion/zara-events:latest \
  --platform=managed \
  --region=us-central1 \
  --verbosity=debug
```

### Getting Help

#### Support Channels
1. **Developer Contact**:
   - **Email**: <EMAIL>
   - **WhatsApp**: +237 651 408 682
   - **Response Time**: Within 24 hours

2. **Documentation**:
   - Check all documentation files in `/docs/`
   - Review implementation guides
   - Check deployment guides

3. **Community Support**:
   - GitHub Issues (if repository is public)
   - Stack Overflow with tag `zara-events`
   - Docker Hub comments

#### Reporting Bugs
When reporting issues, include:
- **Error messages**: Full error text
- **Steps to reproduce**: Detailed steps
- **Environment**: Local/production, browser, OS
- **Logs**: Relevant log entries
- **Screenshots**: If UI-related

This comprehensive troubleshooting guide helps identify and resolve common issues quickly and effectively.
