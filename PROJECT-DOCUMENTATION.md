# 📚 ZARA-Events: Complete Project Documentation

## 📋 Table of Contents

1. [Project Overview](#project-overview)
2. [System Design](#system-design)
3. [Implementation Details](#implementation-details)
4. [Deployment Guide](#deployment-guide)
5. [Code Explanation](#code-explanation)
6. [User Manual](#user-manual)
7. [API Documentation](#api-documentation)
8. [Troubleshooting](#troubleshooting)

---

## 🎯 Project Overview

### Project Information
- **Project Name**: ZARA-Events
- **Version**: 1.0.0
- **Developer**: Tayong Fritz
- **Institution**: ICT University, Yaoundé, Cameroon
- **Contact**: <EMAIL>
- **WhatsApp**: +237 651 408 682
- **Repository**: Docker Hub - zaramillion/zara-events

### Project Description
ZARA-Events is a comprehensive online event booking platform designed specifically for the Central African market. The system provides a modern, user-friendly interface for discovering, booking, and managing event tickets with integrated payment processing and administrative tools.

### Key Features
- **Event Discovery**: Browse and search events by category, date, location
- **User Management**: Registration, authentication, profile management
- **Booking System**: Shopping cart, ticket booking, confirmation
- **Payment Processing**: Simulated payment with multiple methods
- **Admin Panel**: Event management, user management, booking oversight
- **Email Integration**: Automated confirmations and notifications
- **Support System**: Help center, contact forms, FAQ
- **Social Media Integration**: WhatsApp, Facebook, Twitter, Instagram
- **Mobile Responsive**: Optimized for all devices

### Technology Stack
- **Backend**: PHP 8.1
- **Database**: MySQL 8.0
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5.3
- **Email**: PHPMailer with Gmail SMTP
- **Containerization**: Docker
- **Deployment**: Google Cloud Run, Docker Hub
- **Version Control**: Git

### Target Audience
- **Primary**: Event organizers and attendees in Central Africa
- **Secondary**: Event management companies
- **Tertiary**: Educational institutions organizing events

### Project Objectives
1. Simplify event discovery and booking process
2. Provide secure and reliable payment processing
3. Offer comprehensive event management tools
4. Ensure mobile-first responsive design
5. Support local payment methods and currency (XAF)
6. Integrate social media for better reach

---

## 🏗️ System Design

### Architecture Overview
ZARA-Events follows a traditional three-tier architecture:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Presentation   │    │   Application   │    │      Data       │
│     Layer       │◄──►│     Layer       │◄──►│     Layer       │
│                 │    │                 │    │                 │
│ • HTML/CSS/JS   │    │ • PHP Backend   │    │ • MySQL DB      │
│ • Bootstrap UI  │    │ • Business Logic│    │ • File Storage  │
│ • Responsive    │    │ • Email System  │    │ • Session Data  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Database Design

#### Entity Relationship Diagram
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Users    │    │   Events    │    │  Bookings   │
├─────────────┤    ├─────────────┤    ├─────────────┤
│ id (PK)     │    │ id (PK)     │    │ id (PK)     │
│ email       │    │ title       │    │ user_id (FK)│
│ password    │    │ description │    │ event_id(FK)│
│ first_name  │    │ date        │    │ quantity    │
│ last_name   │    │ price       │    │ total_amount│
│ role        │    │ capacity    │    │ status      │
│ created_at  │    │ location    │    │ created_at  │
└─────────────┘    │ category    │    └─────────────┘
                   │ image       │
                   │ created_at  │
                   └─────────────┘
```

#### Database Tables

1. **users**: User account information
2. **events**: Event details and metadata
3. **bookings**: Ticket booking records
4. **cart**: Shopping cart items
5. **payments**: Payment transaction records
6. **user_sessions**: Session management
7. **password_reset_tokens**: Password reset functionality

### System Components

#### 1. Authentication System
- User registration and login
- Password hashing (bcrypt)
- Session management
- Role-based access control (user/admin)
- Password reset functionality

#### 2. Event Management
- Event creation and editing (admin)
- Category management
- Image upload and storage
- Event search and filtering
- Capacity management

#### 3. Booking System
- Shopping cart functionality
- Ticket quantity selection
- Booking confirmation
- QR code generation
- Email notifications

#### 4. Payment Processing
- Simulated payment gateway
- Multiple payment methods
- Transaction logging
- Refund processing
- Receipt generation

#### 5. Email System
- SMTP integration (Gmail)
- Automated confirmations
- Contact form processing
- Admin notifications
- Template-based emails

#### 6. Admin Panel
- Dashboard with analytics
- Event management
- User management
- Booking oversight
- System configuration

### Security Features
- SQL injection prevention (prepared statements)
- XSS protection (input sanitization)
- CSRF protection
- Password hashing
- Session security
- Input validation
- File upload restrictions

### Performance Optimizations
- Database indexing
- Image optimization
- CSS/JS minification
- Caching strategies
- Lazy loading
- CDN integration (Bootstrap, FontAwesome)

---

## 🔧 Implementation Details

### Project Structure
```
zara-events/
├── admin/                  # Admin panel
│   ├── index.php          # Admin dashboard
│   ├── events/            # Event management
│   ├── users/             # User management
│   └── bookings/          # Booking management
├── assets/                # Static assets
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   └── images/            # Image files
├── auth/                  # Authentication
│   ├── login.php          # User login
│   ├── register.php       # User registration
│   └── logout.php         # User logout
├── booking/               # Booking system
│   ├── cart.php           # Shopping cart
│   ├── checkout.php       # Checkout process
│   └── confirmation.php   # Booking confirmation
├── database/              # Database files
│   ├── schema.sql         # Database schema
│   └── init-data.sql      # Sample data
├── events/                # Event pages
│   ├── index.php          # Event listing
│   ├── details.php        # Event details
│   └── search.php         # Event search
├── includes/              # Shared components
│   ├── config.php         # Configuration
│   ├── functions.php      # Utility functions
│   ├── email.php          # Email functionality
│   └── header.php         # Page header
├── user/                  # User dashboard
│   ├── dashboard.php      # User dashboard
│   ├── profile.php        # Profile management
│   └── bookings.php       # User bookings
├── support/               # Support pages
│   ├── about.php          # About page
│   ├── contact.php        # Contact form
│   ├── help-center.php    # Help center
│   ├── privacy-policy.php # Privacy policy
│   └── terms-of-service.php # Terms of service
├── docker-compose.yml     # Docker configuration
├── Dockerfile            # Docker image definition
└── index.php             # Homepage
```

### Core Technologies

#### Backend (PHP 8.1)
- **Framework**: Vanilla PHP with custom MVC pattern
- **Database**: PDO with MySQL
- **Email**: PHPMailer library
- **Session Management**: Native PHP sessions
- **Security**: Custom security functions

#### Frontend
- **CSS Framework**: Bootstrap 5.3.2
- **Icons**: Font Awesome 6.4.0
- **Fonts**: Google Fonts (Poppins, Inter)
- **JavaScript**: Vanilla JS with jQuery
- **Responsive Design**: Mobile-first approach

#### Database (MySQL 8.0)
- **Engine**: InnoDB
- **Character Set**: UTF-8
- **Indexing**: Optimized for performance
- **Relationships**: Foreign key constraints

### Key Implementation Features

#### 1. Modern UI/UX Design
- Soft ivory and tropical teal color scheme
- Gradient backgrounds and modern cards
- Smooth animations and transitions
- Mobile-responsive design
- Accessibility features

#### 2. Email Integration
- Gmail SMTP configuration
- HTML email templates
- Automated booking confirmations
- Contact form processing
- Admin notifications

#### 3. Security Implementation
```php
// Input sanitization
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

// Password hashing
$hashedPassword = password_hash($password, PASSWORD_DEFAULT);

// Prepared statements
$stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
$stmt->execute([$email]);
```

#### 4. Session Management
```php
// Session configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 1);
session_start();
```

#### 5. Error Handling
```php
// Database error handling
try {
    $pdo = new PDO($dsn, $username, $password, $options);
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    die("Database connection failed");
}
```

---

## 🚀 Deployment Guide

### Local Development Setup

#### Prerequisites
- PHP 8.1 or higher
- MySQL 8.0
- Docker and Docker Compose
- Git

#### Quick Start
```bash
# Clone the repository
git clone https://github.com/your-repo/zara-events.git
cd zara-events

# Start with Docker
docker-compose up -d

# Access the application
open http://localhost:9000
```

### Docker Deployment

#### Building the Image
```bash
# Build Docker image
docker build -t zaramillion/zara-events:latest .

# Push to Docker Hub
docker push zaramillion/zara-events:latest
```

#### Running with Docker Compose
```yaml
version: '3.8'
services:
  web:
    image: zaramillion/zara-events:latest
    ports:
      - "9000:80"
    environment:
      - DB_HOST=mysql:3306
      - DB_USER=event_user
      - DB_PASS=event_password
      - DB_NAME=event_booking_system
    depends_on:
      - mysql
  
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: event_booking_system
      MYSQL_USER: event_user
      MYSQL_PASSWORD: event_password
    ports:
      - "3307:3306"
```

### Cloud Deployment

#### Google Cloud Run
```bash
# Deploy to Cloud Run
gcloud run deploy zara-events \
  --image=zaramillion/zara-events:latest \
  --platform=managed \
  --region=us-central1 \
  --allow-unauthenticated \
  --port=80 \
  --memory=1Gi
```

#### Environment Variables for Production
```
ENVIRONMENT=production
DB_HOST=your-database-host
DB_USER=your-database-user
DB_PASS=your-database-password
DB_NAME=event_booking_system
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

### Database Setup

#### Schema Creation
```sql
-- Create database
CREATE DATABASE event_booking_system;
USE event_booking_system;

-- Import schema
SOURCE database/schema.sql;

-- Import sample data
SOURCE database/init-data.sql;
```

#### Production Database Options
1. **Google Cloud SQL**: Managed MySQL service
2. **AWS RDS**: Amazon's managed database
3. **PlanetScale**: Serverless MySQL platform
4. **DigitalOcean Managed Database**: Simple managed database

### SSL/HTTPS Configuration

#### For Cloud Run
- Automatic HTTPS provided
- Custom domain setup available
- SSL certificates managed automatically

#### For VPS/Dedicated Servers
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:9000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Monitoring and Logging

#### Application Logs
```php
// Error logging
error_log("Error message", 3, "/var/log/zara-events.log");

// Custom logging function
function logActivity($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] [$level] $message\n";
    file_put_contents('/var/log/app.log', $logMessage, FILE_APPEND);
}
```

#### Health Checks
```bash
# Docker health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1
```

---

*This is the first part of the comprehensive documentation. The remaining sections (Code Explanation, User Manual, API Documentation, and Troubleshooting) will follow in separate files to maintain readability and organization.*
