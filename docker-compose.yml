services:
  # ZARA-Events Web Application
  web:
    build: .
    image: zaramillion/zara-events:latest
    container_name: zara_events_web
    restart: unless-stopped
    ports:
      - "7823:80"
    environment:
      - DB_HOST=mysql:3306
      - DB_USER=event_user
      - DB_PASS=event_password
      - DB_NAME=event_booking_system
      - ENVIRONMENT=production
    depends_on:
      - mysql
    networks:
      - event_booking_network
    volumes:
      - ./assets/images:/var/www/html/assets/images
      - ./logs:/var/www/html/logs

  # MySQL Database Service
  mysql:
    image: mysql:8.0
    container_name: event_booking_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: event_booking_system
      MYSQL_USER: event_user
      MYSQL_PASSWORD: event_password
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./database/init-data.sql:/docker-entrypoint-initdb.d/02-init-data.sql
    networks:
      - event_booking_network
    command: --default-authentication-plugin=mysql_native_password

  # phpMyAdmin for database management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: event_booking_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: event_user
      PMA_PASSWORD: event_password
      MYSQL_ROOT_PASSWORD: root_password
    ports:
      - "8081:80"
    depends_on:
      - mysql
    networks:
      - event_booking_network

volumes:
  mysql_data:
    driver: local

networks:
  event_booking_network:
    driver: bridge
