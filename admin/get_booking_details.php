<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

// Set JSON header
header('Content-Type: application/json');

$bookingId = (int)($_GET['id'] ?? 0);

if (!$bookingId) {
    echo json_encode(['success' => false, 'message' => 'Invalid booking ID']);
    exit;
}

try {
    // Get booking details with event and user information
    $db->query('SELECT b.*, e.title as event_title, e.event_date, e.event_time, e.venue, e.location,
               u.first_name, u.last_name, u.email as user_email, u.phone as user_phone
               FROM bookings b
               JOIN events e ON b.event_id = e.id
               JOIN users u ON b.user_id = u.id
               WHERE b.id = :booking_id');
    $db->bind(':booking_id', $bookingId);
    $booking = $db->single();
    
    if (!$booking) {
        echo json_encode(['success' => false, 'message' => 'Booking not found']);
        exit;
    }
    
    // Format the booking data
    $bookingData = [
        'booking_reference' => $booking->booking_reference,
        'booking_status' => $booking->booking_status,
        'quantity' => $booking->quantity,
        'total_amount' => number_format($booking->total_amount, 0, '.', ','),
        'created_at' => $booking->created_at,
        'attendee_name' => $booking->attendee_name,
        'attendee_email' => $booking->attendee_email,
        'attendee_phone' => $booking->attendee_phone,
        'event_title' => $booking->event_title,
        'event_date' => $booking->event_date,
        'event_time' => $booking->event_time,
        'venue' => $booking->venue,
        'location' => $booking->location
    ];
    
    echo json_encode([
        'success' => true,
        'booking' => $bookingData
    ]);
    
} catch (Exception $e) {
    error_log("Error fetching booking details: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}
?>
