#!/bin/bash

# ZARA-Events Deployment Verification Script
echo "🔍 ZARA-Events Deployment Verification"
echo "======================================"
echo ""

BASE_URL="http://localhost:9000"
PHPMYADMIN_URL="http://localhost:8081"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to test URL
test_url() {
    local url=$1
    local name=$2
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "200\|302"; then
        echo -e "✅ ${GREEN}$name${NC} - OK"
        return 0
    else
        echo -e "❌ ${RED}$name${NC} - FAILED"
        return 1
    fi
}

# Function to test database connection
test_database() {
    echo "🗄️  Testing Database Connection..."
    
    if docker exec zara_events_web php -r "
        try {
            \$pdo = new PDO('mysql:host=mysql:3306;dbname=event_booking_system', 'event_user', 'event_password');
            echo 'SUCCESS';
        } catch (Exception \$e) {
            echo 'FAILED: ' . \$e->getMessage();
        }
    " 2>/dev/null | grep -q "SUCCESS"; then
        echo -e "✅ ${GREEN}Database Connection${NC} - OK"
        return 0
    else
        echo -e "❌ ${RED}Database Connection${NC} - FAILED"
        return 1
    fi
}

# Check if containers are running
echo "🐳 Checking Docker Containers..."
if docker-compose ps | grep -q "Up"; then
    echo -e "✅ ${GREEN}Docker Containers${NC} - Running"
else
    echo -e "❌ ${RED}Docker Containers${NC} - Not running"
    echo "Please run: docker-compose up -d"
    exit 1
fi

echo ""

# Test database connection
test_database
echo ""

# Test main application endpoints
echo "🌐 Testing Application Endpoints..."
test_url "$BASE_URL" "Main Application"
test_url "$BASE_URL/welcome.php" "Welcome Page"
test_url "$BASE_URL/about.php" "About Page"
test_url "$BASE_URL/contact.php" "Contact Page"
test_url "$BASE_URL/help-center.php" "Help Center"
test_url "$BASE_URL/privacy-policy.php" "Privacy Policy"
test_url "$BASE_URL/terms-of-service.php" "Terms of Service"
test_url "$BASE_URL/events/" "Events Page"
test_url "$BASE_URL/auth/login.php" "Login Page"
test_url "$BASE_URL/auth/register.php" "Registration Page"

echo ""

# Test admin tools
echo "🛠️  Testing Admin Tools..."
test_url "$PHPMYADMIN_URL" "phpMyAdmin"

echo ""

# Test Docker image availability
echo "📦 Testing Docker Hub Image..."
if docker pull zaramillion/zara-events:latest >/dev/null 2>&1; then
    echo -e "✅ ${GREEN}Docker Hub Image${NC} - Available"
else
    echo -e "❌ ${RED}Docker Hub Image${NC} - Not available"
fi

echo ""

# Check container health
echo "💚 Checking Container Health..."
HEALTH_STATUS=$(docker inspect zara_events_web --format='{{.State.Health.Status}}' 2>/dev/null)
if [ "$HEALTH_STATUS" = "healthy" ]; then
    echo -e "✅ ${GREEN}Container Health${NC} - Healthy"
else
    echo -e "⚠️  ${YELLOW}Container Health${NC} - $HEALTH_STATUS"
fi

echo ""

# Display container information
echo "📊 Container Information:"
docker-compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"

echo ""

# Display useful URLs
echo "🔗 Quick Access URLs:"
echo "   Main App:     $BASE_URL"
echo "   About:        $BASE_URL/about.php"
echo "   Contact:      $BASE_URL/contact.php"
echo "   Help Center:  $BASE_URL/help-center.php"
echo "   phpMyAdmin:   $PHPMYADMIN_URL"

echo ""

# Display management commands
echo "🛠️  Management Commands:"
echo "   View logs:    docker-compose logs -f web"
echo "   Restart:      docker-compose restart web"
echo "   Stop all:     docker-compose down"
echo "   Start all:    docker-compose up -d"

echo ""

# Final status
echo "🎉 Verification Complete!"
echo ""
echo "📧 Email Configuration:"
echo "   SMTP: smtp.gmail.com:587"
echo "   From: <EMAIL>"
echo "   To:   <EMAIL>"

echo ""
echo "👨‍💻 Developer: Tayong Fritz"
echo "🏫 Institution: ICT University, Yaoundé"
echo "📱 WhatsApp: +237 651 408 682"
echo ""
echo "🐳 Docker Hub: https://hub.docker.com/r/zaramillion/zara-events"
