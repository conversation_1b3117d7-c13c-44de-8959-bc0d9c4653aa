<?php
require_once 'includes/config.php';
require_once 'includes/email.php';

// Only allow access to admin users or in development
if (!isAdmin() && getenv('ENVIRONMENT') === 'production') {
    die('Access denied');
}

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $testType = $_POST['test_type'] ?? '';
    
    global $emailManager;
    
    switch ($testType) {
        case 'basic':
            if ($emailManager->sendTestEmail()) {
                $message = 'Test email sent successfully! Check your inbox at ' . ADMIN_EMAIL;
                $messageType = 'success';
            } else {
                $message = 'Failed to send test email. Check your email configuration.';
                $messageType = 'error';
            }
            break;
            
        case 'contact':
            if ($emailManager->sendContactMessage(
                'Test User',
                '<EMAIL>',
                'Test Contact Message',
                'This is a test contact message to verify the contact form functionality.'
            )) {
                $message = 'Test contact message sent successfully!';
                $messageType = 'success';
            } else {
                $message = 'Failed to send test contact message.';
                $messageType = 'error';
            }
            break;
            
        case 'custom':
            $toEmail = $_POST['to_email'] ?? ADMIN_EMAIL;
            $subject = $_POST['subject'] ?? 'Custom Test Email';
            $messageText = $_POST['message'] ?? 'This is a custom test email.';
            
            if (sendEmail($toEmail, 'Test Recipient', $subject, $messageText)) {
                $message = "Custom test email sent to $toEmail successfully!";
                $messageType = 'success';
            } else {
                $message = 'Failed to send custom test email.';
                $messageType = 'error';
            }
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Testing - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/modern-ui.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-envelope me-2"></i>
                            Email Testing Panel
                        </h3>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
                                <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                                <?php echo htmlspecialchars($message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <!-- Email Configuration Info -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-cog me-2"></i>
                                    Current Email Configuration
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>SMTP Host:</strong> <?php echo SMTP_HOST; ?></p>
                                        <p><strong>SMTP Port:</strong> <?php echo SMTP_PORT; ?></p>
                                        <p><strong>Encryption:</strong> <?php echo SMTP_ENCRYPTION; ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>From Email:</strong> <?php echo FROM_EMAIL; ?></p>
                                        <p><strong>From Name:</strong> <?php echo FROM_NAME; ?></p>
                                        <p><strong>Admin Email:</strong> <?php echo ADMIN_EMAIL; ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Test Options -->
                        <div class="row">
                            <!-- Basic Test -->
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-paper-plane fa-3x text-primary mb-3"></i>
                                        <h5>Basic Test</h5>
                                        <p class="text-muted">Send a simple test email to verify SMTP configuration</p>
                                        <form method="POST">
                                            <input type="hidden" name="test_type" value="basic">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-paper-plane me-2"></i>Send Test
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Form Test -->
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-envelope fa-3x text-success mb-3"></i>
                                        <h5>Contact Form Test</h5>
                                        <p class="text-muted">Test the contact form email functionality</p>
                                        <form method="POST">
                                            <input type="hidden" name="test_type" value="contact">
                                            <button type="submit" class="btn btn-success">
                                                <i class="fas fa-envelope me-2"></i>Test Contact
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Custom Test -->
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-edit fa-3x text-warning mb-3"></i>
                                        <h5>Custom Test</h5>
                                        <p class="text-muted">Send a custom email with your own content</p>
                                        <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#customEmailModal">
                                            <i class="fas fa-edit me-2"></i>Custom Email
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mt-4 text-center">
                            <a href="contact.php" class="btn btn-outline-primary me-2">
                                <i class="fas fa-external-link-alt me-2"></i>Test Contact Page
                            </a>
                            <a href="/" class="btn btn-outline-secondary">
                                <i class="fas fa-home me-2"></i>Back to Home
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Email Modal -->
    <div class="modal fade" id="customEmailModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>
                        Send Custom Test Email
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="test_type" value="custom">
                        
                        <div class="mb-3">
                            <label for="to_email" class="form-label">To Email</label>
                            <input type="email" class="form-control" id="to_email" name="to_email" value="<?php echo ADMIN_EMAIL; ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject</label>
                            <input type="text" class="form-control" id="subject" name="subject" value="Custom Test Email from ZARA-Events" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Message</label>
                            <textarea class="form-control" id="message" name="message" rows="5" required>Hello!

This is a custom test email from the ZARA-Events platform.

If you receive this email, it means the email configuration is working correctly.

Best regards,
ZARA-Events Team</textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>Send Email
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
