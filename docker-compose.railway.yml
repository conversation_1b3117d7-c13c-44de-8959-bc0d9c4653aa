# Docker Compose for Railway-like Environment Testing
# This setup mimics Railway's internal network structure for local testing

services:
  # ZARA-Events Web Application (Railway-like setup)
  web:
    image: zaramillion/zara-events:latest
    container_name: zara_events_railway_test
    restart: unless-stopped
    ports:
      - "9000:80"
    environment:
      # Railway-style environment variables
      - MYSQLHOST=mysql.railway.internal
      - MYSQLPORT=3306
      - MYSQLUSER=root
      - MYSQLPASSWORD=DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU
      - MYSQLDATABASE=railway
      - ENVIRONMENT=production
      - SITE_URL=http://localhost:9000
      # Email configuration
      - SMTP_HOST=smtp.gmail.com
      - SMTP_PORT=587
      - SMTP_USERNAME=<EMAIL>
      - SMTP_PASSWORD=pvjc rjit ogxg ncce
      - SMTP_ENCRYPTION=tls
      - FROM_EMAIL=<EMAIL>
      - FROM_NAME=ZARA-Events
    depends_on:
      - mysql
    networks:
      - railway_network
    volumes:
      - ./assets/images:/var/www/html/assets/images
      - ./logs:/var/www/html/logs

  # MySQL Database Service (Railway-like setup)
  mysql:
    image: mysql:8.0
    container_name: railway_mysql_test
    restart: unless-stopped
    hostname: mysql.railway.internal
    environment:
      MYSQL_ROOT_PASSWORD: DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU
      MYSQL_DATABASE: railway
      MYSQL_USER: railwayuser
      MYSQL_PASSWORD: railwaypass
    ports:
      - "3308:3306"  # Different port to avoid conflicts
    volumes:
      - railway_mysql_data:/var/lib/mysql
      - ./database/railway-setup.sql:/docker-entrypoint-initdb.d/01-railway-setup.sql
    networks:
      - railway_network
    command: --default-authentication-plugin=mysql_native_password

  # phpMyAdmin for database management (Railway-like)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: railway_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql.railway.internal
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU
      MYSQL_ROOT_PASSWORD: DWxHAepHAaohXaSaPxQzaaPfPXTfLAAU
    ports:
      - "8082:80"
    depends_on:
      - mysql
    networks:
      - railway_network

volumes:
  railway_mysql_data:
    driver: local

networks:
  railway_network:
    driver: bridge
    name: railway_network
